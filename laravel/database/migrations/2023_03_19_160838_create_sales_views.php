<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $query = DB::table("sales")
            ->select([
                DB::raw("distinct crm_sales.quantity as sales_quantity"),
                "sales.bonus AS sales_bonus",
                "sales.region AS sales_region",
                "sales.value AS sales_value",
                "sales.date AS sales_date",
                "sales_details.date AS sales_details_date",
                "sales_details.quantity AS sales_details_quantity",
                "sales_details.bonus AS sales_details_bonus",
                "sales_details.value AS sales_details_value",
                "products.ucode AS product_ucode",
                "products.name AS product_name",
                "products.short_name AS product_short_name",
                "products.quantity AS product_quantity",
                "products.notes AS product_notes",
                "products.sort AS product_sort",
                "products.launch_date AS product_launch_date",
                "bricks.name AS brick_name",
                "bricks.notes AS brick_notes",
                "bricks.sort AS brick_sort",
                "line_divisions.name AS division_name",
                "line_divisions.notes AS division_notes"
            ])
            ->join("sales_details", "sales.id", "sales_details.sale_id")
            ->join("products", "sales.product_id", "products.id")
            ->join("bricks", "sales_details.brick_id", "bricks.id")
            ->join("line_divisions", "sales_details.div_id", "line_divisions.id")
            ->toSql();
        DB::statement(
            "CREATE OR REPLACE VIEW  sales_view  AS $query");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('DROP VIEW IF EXISTS sales_views');
    }
}
;
