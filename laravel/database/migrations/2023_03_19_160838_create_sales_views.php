<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Original sales view
        $query = DB::table("sales")
            ->select([
                DB::raw("distinct crm_sales.quantity as sales_quantity"),
                "sales.bonus AS sales_bonus",
                "sales.region AS sales_region",
                "sales.value AS sales_value",
                "sales.date AS sales_date",
                "sales_details.date AS sales_details_date",
                "sales_details.quantity AS sales_details_quantity",
                "sales_details.bonus AS sales_details_bonus",
                "sales_details.value AS sales_details_value",
                "products.ucode AS product_ucode",
                "products.name AS product_name",
                "products.short_name AS product_short_name",
                "products.quantity AS product_quantity",
                "products.notes AS product_notes",
                "products.sort AS product_sort",
                "products.launch_date AS product_launch_date",
                "bricks.name AS brick_name",
                "bricks.notes AS brick_notes",
                "bricks.sort AS brick_sort",
                "line_divisions.name AS division_name",
                "line_divisions.notes AS division_notes"
            ])
            ->join("sales_details", "sales.id", "sales_details.sale_id")
            ->join("products", "sales.product_id", "products.id")
            ->join("bricks", "sales_details.brick_id", "bricks.id")
            ->join("line_divisions", "sales_details.div_id", "line_divisions.id")
            ->toSql();
        DB::statement(
            "CREATE OR REPLACE VIEW  sales_view  AS $query");

        // Sales Details Service view for detailed reporting
        $this->createSalesDetailsView();
    }

    private function createSalesDetailsView()
    {
        $product_avg_price = '
            COALESCE(
                (
                    SELECT avg_price FROM crm_product_prices
                    WHERE crm_product_prices.product_id = crm_sales.product_id
                    AND crm_product_prices.deleted_at IS NULL
                    AND crm_product_prices.distributor_id = crm_sales.distributor_id
                    AND crm_product_prices.from_date <= crm_sales_details.date
                    AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_sales_details.date)
                    ORDER BY crm_product_prices.from_date DESC
                    LIMIT 1
                ),
                (
                    SELECT avg_price FROM crm_product_prices
                    WHERE crm_product_prices.product_id = crm_sales.product_id
                    AND crm_product_prices.deleted_at IS NULL
                    AND crm_product_prices.distributor_id IS NULL
                    AND crm_product_prices.from_date <= crm_sales_details.date
                    AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_sales_details.date)
                    ORDER BY crm_product_prices.from_date DESC
                    LIMIT 1
                )
        )';

        DB::statement("
            CREATE OR REPLACE VIEW sales_details_view AS
            SELECT DISTINCT
                crm_sales_details.id as detail_id,
                crm_sales.id as id,
                crm_lines.name as line,
                crm_line_divisions.name as division,
                crm_sales_details.div_id as div_id,
                IFNULL(crm_bricks.name,'') AS brick,
                IFNULL(crm_sales_details.brick_id,'') AS brick_id,
                DATE_FORMAT(crm_sales_details.date,'%Y-%m-%d') as date,
                IFNULL(crm_higher.fullname,'') AS manager,
                (SELECT IFNULL(crm_users.fullname,'')
                 FROM crm_users
                 JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
                 WHERE crm_line_users_divisions.line_division_id = crm_sales_details.div_id
                 AND crm_line_users_divisions.deleted_at IS NULL
                 AND crm_users.deleted_at IS NULL
                 AND crm_line_users_divisions.from_date <= crm_sales_details.date
                 AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_sales_details.date)
                 LIMIT 1) as employee,
                (SELECT IFNULL(crm_users.emp_code,'')
                 FROM crm_users
                 JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
                 WHERE crm_line_users_divisions.line_division_id = crm_sales_details.div_id
                 AND crm_line_users_divisions.deleted_at IS NULL
                 AND crm_users.deleted_at IS NULL
                 AND crm_line_users_divisions.from_date <= crm_sales_details.date
                 AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_sales_details.date)
                 LIMIT 1) as emp_code,
                IFNULL(crm_distributors.name,'') AS distributor,
                crm_mappings.distributor_id,
                crm_sales_types.name as type,
                crm_mappings.name as name,
                crm_mappings.id as mapping_id,
                crm_mappings.code as mapping_code,
                CASE
                    WHEN crm_sales.ceiling = '0' THEN 'NORMAL'
                    WHEN crm_sales.ceiling = '2' THEN 'DISTRIBUTED'
                END as distribution_type,
                IFNULL(crm_mappings.address,'') AS address,
                crm_sales.product_id,
                crm_products.name as product,
                crm_division_types.color as color,
                FORMAT((crm_sales_details.quantity),2) as units,
                FORMAT(($product_avg_price),2) as pro_price,
                crm_sales_details.bonus as bonus,
                CASE
                    WHEN crm_sales.value = 0 THEN FORMAT(($product_avg_price * crm_sales_details.quantity),2)
                    ELSE FORMAT(crm_sales_details.value,2)
                END as value,
                crm_sales_details.ratio
            FROM crm_sales
            JOIN crm_products ON crm_sales.product_id = crm_products.id
            JOIN crm_sales_details ON crm_sales.id = crm_sales_details.sale_id
            LEFT JOIN crm_mapping_sale ON crm_sales.id = crm_mapping_sale.sale_id
            LEFT JOIN crm_target_details ON crm_sales.product_id = crm_target_details.product_id
                AND crm_sales_details.div_id = crm_target_details.div_id
                AND crm_sales_details.brick_id = crm_target_details.brick_id
                AND crm_sales_details.date = crm_target_details.date
            JOIN crm_mappings ON crm_mapping_sale.mapping_id = crm_mappings.id
            LEFT JOIN crm_distributors ON crm_sales.distributor_id = crm_distributors.id
            LEFT JOIN crm_sales_types ON crm_mappings.mapping_type_id = crm_sales_types.id
            LEFT JOIN crm_bricks ON crm_sales_details.brick_id = crm_bricks.id
            LEFT JOIN crm_line_divisions ON crm_sales_details.div_id = crm_line_divisions.id
            LEFT JOIN crm_division_types ON crm_line_divisions.division_type_id = crm_division_types.id
            LEFT JOIN crm_line_users_divisions as low_level ON crm_line_divisions.id = low_level.line_division_id
                AND low_level.deleted_at IS NULL
            LEFT JOIN crm_users as mr ON low_level.user_id = mr.id
            LEFT JOIN crm_line_div_parents ON crm_line_divisions.id = crm_line_div_parents.line_div_id
                AND crm_line_div_parents.deleted_at IS NULL
            LEFT JOIN crm_line_divisions as parent ON crm_line_div_parents.parent_id = parent.id
            LEFT JOIN crm_line_users_divisions as high_level ON crm_line_div_parents.parent_id = high_level.line_division_id
            LEFT JOIN crm_users as higher ON high_level.user_id = higher.id
            LEFT JOIN crm_lines ON crm_line_divisions.line_id = crm_lines.id
            WHERE crm_sales.ceiling IN ('0', '2')
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('DROP VIEW IF EXISTS sales_view');
        DB::statement('DROP VIEW IF EXISTS sales_details_view');
    }
}
;
