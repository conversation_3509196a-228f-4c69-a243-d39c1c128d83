<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('p_v_s', function (Blueprint $table) {
            $table->text('additional_info')->nullable()->after('submition_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('p_v_s', function (Blueprint $table) {
            $table->dropColumn('additional_info');
        });
    }
};
