<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drug_information', function (Blueprint $table) {
            $table->string('route_adminstration')->nullable()->change();
        });
        Schema::table('patient_information', function (Blueprint $table) {
            $table->string('medical_history')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drug_information', function (Blueprint $table) {
            $table->string('route_adminstration')->nullable(false)->change();
        });
        Schema::table('patient_information', function (Blueprint $table) {
            $table->string('medical_history')->nullable(false)->change();
        });
    }
};
