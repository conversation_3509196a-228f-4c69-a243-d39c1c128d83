<?php

namespace Database\Seeders\Permissions;

use App\Form;
use App\Module;
use App\Permission;
use Illuminate\Database\Seeder;

class AccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // accounts

        resetPermissionModule("accounts");
        $module = Module::firstOrCreate([
            "module" => "accounts",
            "icon" => "cil-people",
        ]);

        $forms = [
            [
                'form' => 'classes'
            ],
            [
                'form' => 'bricks'
            ],
            [
                'form' => 'specialities'
            ],
            [
                'form' => 'shifts'
            ],
            [
                'form' => 'account_types'
            ],
            [
                'form' => 'accounts'
            ],
            [
                'form' => 'account_lines'
            ],
            [
                'form' => 'doctors'
            ],
            [
                'form' => 'account_doctors'
            ],
            [
                'form' => 'levels'
            ],
            [
                'form' => 'personalitytypes'
            ],
            [
                'form' => 'account_socials'
            ],
            [
                'form' => 'doctor_socials'
            ],
            [
                'form' => 'list_type_settings'
            ],
            [
                'form' => 'favourite_lists'
            ],
            [
                'form' => 'kol_lists'
            ],
            [
                'form' => 'list_report_actions'
            ],
            [
                'form' => 'classifications'
            ],
        ];

        $formIds = [];
        foreach ($forms as $value) {
            $form = Form::firstOrCreate($value);
            array_push($formIds, $form->id);
        }

        $permissions = [
            // classes
            [
                'name'          => 'show_all_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],



            [
                'name'          => 'import_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_classes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_class',
                'guard_name'    => 'api',
                'form_id'       => $formIds[0],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Bricks
            [
                'name'          => 'show_all_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_brick_view_plans',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            [
                'name'          => 'import_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_bricks_unified',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '40',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_bricks',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_brick',
                'guard_name'    => 'api',
                'form_id'       => $formIds[1],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],



            // Specialities
            [
                'name'          => 'show_all_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_specialities',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_speciality',
                'guard_name'    => 'api',
                'form_id'       => $formIds[2],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],



            // Shifts
            [
                'name'          => 'show_all_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_shifts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_shift',
                'guard_name'    => 'api',
                'form_id'       => $formIds[3],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Account Types
            [
                'name'          => 'show_all_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Sub Account Types

            [
                'name'          => 'show_all_sub_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_account_types',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_account_type',
                'guard_name'    => 'api',
                'form_id'       => $formIds[4],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Accounts
            [
                'name'          => 'show_all_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_account',
                'guard_name'    => 'api',
                'form_id'       => $formIds[5],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Account Lines
            [
                'name'          => 'show_all_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_account_lines',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_account_line',
                'guard_name'    => 'api',
                'form_id'       => $formIds[6],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Doctors
            [
                'name'          => 'show_all_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_doctor',
                'guard_name'    => 'api',
                'form_id'       => $formIds[7],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Account Doctors
            [
                'name'          => 'show_all_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_account_doctors',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_account_doctor',
                'guard_name'    => 'api',
                'form_id'       => $formIds[8],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Levels
            [
                'name'          => 'show_all_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_levels',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_level',
                'guard_name'    => 'api',
                'form_id'       => $formIds[9],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // Personality Types

            [
                'name'          => 'show_all_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_personalitytypes',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_personalitytype',
                'guard_name'    => 'api',
                'form_id'       => $formIds[10],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            // Tools Permissions



            // account social
            [
                'name'          => 'show_all_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_account_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_account_social',
                'guard_name'    => 'api',
                'form_id'       => $formIds[11],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Doctor Socials
            [
                'name'          => 'show_all_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'destroy_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '8',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_doctor_socials',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '18',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_view_single_doctor_social',
                'guard_name'    => 'api',
                'form_id'       => $formIds[12],
                'action_id'     => '19',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // List Accounts Type 

            [
                'name'          => 'show_all_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'restore_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '6',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_all_archive_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '7',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'import_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '10',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_xlsx_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '11',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_csv_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '22',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_pdf_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '12',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'export_email_list_type_settings',
                'guard_name'    => 'api',
                'form_id'       => $formIds[13],
                'action_id'     => '13',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Favourite List
            // List Accounts Type 

            [
                'name'          => 'show_all_favourite_lists',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_favourite_lists',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_favourite_lists',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_favourite_lists',
                'guard_name'    => 'api',
                'form_id'       => $formIds[14],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Favourite List
            // List Accounts Type 

            [
                'name'          => 'show_all_kol_lists',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_kol_lists',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'reset_kol_lists',
                'guard_name'    => 'api',
                'form_id'       => $formIds[15],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],


            // List Report Actions 
            [
                'name'          => 'show_action_dropdown',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'all_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'active_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'inactive_accounts',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'reset_account_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '24',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'reset_favourite_list',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '24',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'master_list',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'verify_locations',
                'guard_name'    => 'api',
                'form_id'       => $formIds[16],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

            // Classifications
            [
                'name'          => 'show_all_account_classifications',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '1',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'create_account_classifications',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '2',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'show_single_account_classifications',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '3',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'edit_account_classifications',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '4',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'name'          => 'delete_account_classifications',
                'guard_name'    => 'api',
                'form_id'       => $formIds[17],
                'action_id'     => '5',
                'module_id'     => $module->id,
                'created_at'    => now(),
                'updated_at'    => now()
            ],

        ];

        foreach ($permissions as $value) {
            Permission::insert($value);
        }
    }
}
