<?php

use App\Http\Controllers\AccountLocationComparisonController;
use App\Http\Controllers\BranchSaleDetailsReportController;
use App\Http\Controllers\BudgetAnalysisController;
use App\Http\Controllers\BudgetConsumptionReportController;
use App\Http\Controllers\BudgetDetailsReportController;
use App\Http\Controllers\CallRateReportController;
use App\Http\Controllers\CeilingReportController;
use App\Http\Controllers\CoachingHeaderController;
use App\Http\Controllers\CommercialBillsReportController;
use App\Http\Controllers\CommercialCostsReportController;
use App\Http\Controllers\CommercialDataController;
use App\Http\Controllers\CommercialReportController;
use App\Http\Controllers\CommercialStatisticsReportController;
use App\Http\Controllers\CustodyController;
use App\Http\Controllers\CustodyReportController;
use App\Http\Controllers\CustomerVisitsReportController;
use App\Http\Controllers\DetailingStatisticsReportController;
use App\Http\Controllers\EmployeeRouteController;
use App\Http\Controllers\EmployeeTrackingReportController;
use App\Http\Controllers\FrequencyReportController;
use App\Http\Controllers\HelicopterViewReportController;
use App\Http\Controllers\ListReportController;
use App\Http\Controllers\ListStatisticsReportController;
use App\Http\Controllers\LogActivityController;
use App\Http\Controllers\ManagersReportController;
use App\Http\Controllers\MappingReportController;
use App\Http\Controllers\OutOfLocationReportController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductFrequencyReportController;
use App\Http\Controllers\ProductMappingReportController;
use App\Http\Controllers\Sales\SaleAchievementComparisonController;
use App\Http\Controllers\Sales\SalesIncentiveCalculationReportController;
use App\Http\Controllers\SalesAchievementReportController;
use App\Http\Controllers\SalesEmployeesReportController;
use App\Http\Controllers\SalesSummeryReportController;
use App\Http\Controllers\StructureReportController;
use App\Http\Controllers\TeamLocationsController;
use App\Http\Controllers\UnifiedSalesDetailsReportController;
use App\Http\Controllers\VacationStatisticsReportController;
use App\Http\Controllers\VisitCoverageReportController;
use App\Http\Controllers\VisitsReportController;
use App\Http\Controllers\VisitStatisticsController;
use App\Http\Controllers\ExpenseReportController;
use App\Http\Controllers\ExpenseStatisticsReportController;
use App\Http\Controllers\SalesPerDistributorReportController;
use App\Http\Controllers\VisitSpecialitiesStatsticsReportController;
use App\Http\Controllers\DoctorTracingReportController;
use App\Http\Controllers\SampleConsumptionReportController;
use App\Http\Controllers\DoctorProfilingReportController;
use App\Http\Controllers\EmployeePerformanceReportController;
use App\Http\Controllers\EndCommercialPaymentsReportController;
use App\Http\Controllers\ExpenseStatisticsReportV2Controller;
use App\Http\Controllers\FinanceOrderReportController;
use App\Http\Controllers\FinanceSummaryReportConroller;
use App\Http\Controllers\FrequencyDetailsViewController;
use App\Http\Controllers\FrequencyReportClassicViewController;
use App\Http\Controllers\KpisReportController;
use App\Http\Controllers\VacationReportController;
use App\Http\Controllers\GiveawayConsumptionReportController;
use App\Http\Controllers\KpisPolicyReportController;
use App\Http\Controllers\LinesDataApiController;
use App\Http\Controllers\LinkedPharmaciesController;
use App\Http\Controllers\LinkedPharmaciesReportController;
use App\Http\Controllers\ListPerSpecialityReportController;
use App\Http\Controllers\ManagerCallrateReportController;
use App\Http\Controllers\ManagerCoverageReportController;
use App\Http\Controllers\ManagerFrequencyReportController;
use App\Http\Controllers\MaterialCostReportController;
use App\Http\Controllers\MaterialStatisticsReportController;
use App\Http\Controllers\MaterialReportController;
use App\Http\Controllers\NotificationReportController;
use App\Http\Controllers\OrderRequestReportController;
use App\Http\Controllers\PlanVisitCoverageReportController;
use App\Http\Controllers\PostKpisReportController;
use App\Http\Controllers\ProductCallsPerSpecialityReport;
use App\Http\Controllers\ProductCallsPerSpecialityReportController;
use App\Http\Controllers\ProductMessagesReportController;
use App\Http\Controllers\PvController;
use App\Http\Controllers\QuizResultSuumaryReportController;
use App\Http\Controllers\RawReportController;
use App\Http\Controllers\SalesAchievementPerBrandController;
use App\Http\Controllers\SalesDetailsReportController;
use App\Http\Controllers\SalesStartPointController;
use App\Http\Controllers\StructureDiagramReportController;
use App\Http\Controllers\TargetDetailsReportController;
use App\Http\Controllers\TargetSummaryReportController;
use App\Http\Controllers\SalesSummaryUnifiedReportController;
use App\Http\Controllers\SampleConsumptionVerticalController;
use App\Http\Controllers\ServiceCompleteReportController;
use App\Http\Controllers\TrainingCategoryReportController;
use App\Http\Controllers\VisitStatisticsPerBrickController;
use App\Http\Controllers\WorkingHoursReportController;
use Illuminate\Support\Facades\Route;


Route::post('/get-employees', [VisitsReportController::class, 'getEmployeesOrDivisions'])->name(''); //TODO:permission is not implemented yet
// structure Report
Route::get('/structureLines', [StructureReportController::class, 'getData'])->name(''); //TODO:permission is not implemented yet
Route::post('/structure-divisions', [StructureReportController::class, 'getDivisions'])->name('');
Route::post('/structure-report/{line}', [StructureReportController::class, 'filteration'])->name('show_reports_structure');
Route::post('/raw-report/{line}', [RawReportController::class, 'filteration'])->name('show_reports_structure');
Route::post('/unlinked-bricks', [StructureReportController::class, 'unlinkedBricks'])->name('show_reports_structure');

// metabase Report
Route::get('/metabase', [StructureReportController::class, 'getReport'])->name(''); //TODO:permission is not implemented yet


// Pv Report
Route::post('/pv-report', [PvController::class, 'filter'])->name('show_reports_pv');

// Product Report
Route::get('/productReport', [ProductController::class, 'productReport'])->name(''); //TODO:permission is not implemented yet
Route::post('/filter', [ProductController::class, 'filter'])->name('show_reports_product');

// List Statistics Report
Route::get('/listStatisticsLines', [ListStatisticsReportController::class, 'getLines'])->name(''); //TODO:permission is not implemented yet
Route::get('/data-for-list-statistics/{line}', [ListStatisticsReportController::class, 'getDataWithLine'])->name(''); //TODO:permission is not implemented yet
Route::post('/list-statistics', [ListStatisticsReportController::class, 'filter'])->name('show_reports_list_statistics');
Route::post('show-list-statistics', [ListStatisticsReportController::class, 'showData'])->name('');

// List Per Speciality Report
Route::get('/listPerSpecialityLines', [ListPerSpecialityReportController::class, 'getLines'])->name(''); //TODO:permission is not implemented yet
Route::post('/data-for-list-per-speciality', [ListPerSpecialityReportController::class, 'getDataWithLine'])->name(''); //TODO:permission is not implemented yet
Route::post('/list-per-speciality', [ListPerSpecialityReportController::class, 'filter'])->name('show_reports_list_statistics');
Route::post('show-list-per-speciality', [ListPerSpecialityReportController::class, 'showData'])->name('');


// Visit Statistics Report
Route::post('/get-positions', [VisitStatisticsController::class, 'getPositions'])->name('');
Route::post('/position-users', [VisitStatisticsController::class, 'getPositionUsers'])->name('');
Route::post('/visit-statistics', [VisitStatisticsController::class, 'filter'])->name('show_reports_visits_statistics');
Route::post('/get-account-types', [VisitStatisticsController::class, 'getAccountTypes'])->name('show_reports_visits_statistics');
Route::post('show-visit-statistics', [VisitStatisticsController::class, 'showData'])->name('');


// Visit Statistics Per Brick Report
Route::post('/visit-statistics-per-brick', [VisitStatisticsPerBrickController::class, 'filter'])->name('show_reports_visits_statistics');

// Detailing Statistics Report
Route::post('/detailing-statistics/{line}', [DetailingStatisticsReportController::class, 'filter'])->name('show_reports_detailing_statistics');
Route::post('show-detailing-statistics', [DetailingStatisticsReportController::class, 'showData'])->name('');

// Samples Consumption Report
Route::post('/get-samples-products', [SampleConsumptionReportController::class, 'getProducts'])->name('show_reports_samples_consumption');
Route::post('/samples-consumption/{line}', [SampleConsumptionReportController::class, 'filter'])->name('show_reports_samples_consumption');
Route::post('show-samples-consumption', [SampleConsumptionReportController::class, 'showData'])->name('');
Route::post('/samples-consumption-vertical', [SampleConsumptionVerticalController::class, 'filter'])->name('show_reports_samples_consumption');
Route::post('/show-samples-consumption-vertical', [SampleConsumptionVerticalController::class, 'showData'])->name('');

// Giveaways Consumption Report
Route::post('/giveaways-consumption/{line}', [GiveawayConsumptionReportController::class, 'filter'])->name('show_reports_giveaways_consumption');
Route::post('show-giveaways-consumption', [GiveawayConsumptionReportController::class, 'showData'])->name('');

// Vacation Statistics Report
Route::get('/vacation-report-lines', [VacationStatisticsReportController::class, 'lines'])->name('');
Route::post('/vacation-statistics', [VacationStatisticsReportController::class, 'filter'])->name('show_reports_vacation_statistics');
Route::post('show-vacation-data', [VacationStatisticsReportController::class, 'getVacationData'])->name('');


// Vacations Report
Route::post('get-vacation-line-data', [VacationReportController::class, 'getLineData'])->name('');
Route::post('/vacations-report', [VacationReportController::class, 'filter'])->name('show_reports_vacation_details');
Route::post('/vacations-download-excel', [VacationReportController::class, 'downloadExcel'])->name('show_reports_vacation_details');


// Coverage Report Version 2
Route::post('/visit-coverage', [VisitCoverageReportController::class, 'filter'])->name('show_reports_coverage');
Route::post('show-coverage', [VisitCoverageReportController::class, 'showData'])->name('');



// Product Calls Per speciality Report
Route::post('/product-calls-per-speciality', [ProductCallsPerSpecialityReportController::class, 'filter'])->name('show_reports_product_frequency');
Route::post('show-product-calls-per-speciality', [ProductCallsPerSpecialityReportController::class, 'showData'])->name('');

// Coverage Report for Plan Version 2
Route::post('/visit-plan-coverage', [PlanVisitCoverageReportController::class, 'filter'])->name('show_reports_coverage');
Route::post('show-plan-coverage', [PlanVisitCoverageReportController::class, 'showData'])->name('');

// Call Rate Report
Route::get('/call-rate-report-lines', [CallRateReportController::class, 'lines'])->name(''); //TODO:permission is not implemented yet
Route::post('/call-rate-report/{line}', [CallRateReportController::class, 'filter'])->name('show_reports_call_rate');
Route::post('/show-callrate-stat', [CallRateReportController::class, 'showData'])->name('');

// managers Report
Route::get('/get-lines-manager-report', [ManagersReportController::class, 'lines'])->name('');
Route::post('/get-types-manager-report', [ManagersReportController::class, 'positions'])->name('');
Route::post('/get-managers', [ManagersReportController::class, 'filter'])->name('');
Route::post('/get-employees-data', [ManagersReportController::class, 'belowEmployeesData'])->name('');
Route::post('/show-managers-data', [ManagersReportController::class, 'showData'])->name('');


// managers Coverage - CallRate - Frequency
Route::post('/get-managers-coverage', [ManagerCoverageReportController::class, 'filter'])->name('');
Route::post('/show-managers-coverage', [ManagerCoverageReportController::class, 'show'])->name('');
Route::post('/get-managers-callrate', [ManagerCallrateReportController::class, 'filter'])->name('');
Route::post('/show-managers-callrate', [ManagerCallrateReportController::class, 'show'])->name('');
Route::post('/get-managers-frequency', [ManagerFrequencyReportController::class, 'filter'])->name('');
Route::post('/show-managers-frequency', [ManagerFrequencyReportController::class, 'show'])->name('');



// Mapping Report
Route::get('/lines-mapping', [MappingReportController::class, 'getLines'])->name('');
Route::post('/getDataLineReport', [MappingReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/mapping-report', [MappingReportController::class, 'filter'])->name('show_reports_mapping');
Route::post('/error-mapping-report', [MappingReportController::class, 'filterErrorMapping'])->name('show_error_reports_mapping');

// Product Mapping Report
Route::get('/lines-product-mapping', [ProductMappingReportController::class, 'getLines'])->name('');
Route::post('/line-data-product-mapping', [ProductMappingReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/product-mapping-report', [ProductMappingReportController::class, 'filter'])->name('show_reports_product_mapping');

// Sales Details Report
Route::post('/line-data-sales-details', [SalesDetailsReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/sales-details-report', [SalesDetailsReportController::class, 'filter'])->name('show_reports_sales_details');
Route::post('/sales-details-report/download', [SalesDetailsReportController::class, 'downloadExcel'])->name(''); //TODO:permission is not implemented yet


Route::post('/unified-sales-details', [UnifiedSalesDetailsReportController::class, 'filter'])->name('show_reports_unified_sales');
Route::post('/unified-sales-details/download', [UnifiedSalesDetailsReportController::class, 'downloadExcel'])->name('show_reports_unified_sales'); //TODO:permission is not implemented yet

Route::post("/sales-achievement-comparison/lines",[SaleAchievementComparisonController::class,'getValidLines'])->name('show_sales-achievement-comparison'); // TODO:permission is not implemented yet
Route::post("/sales-achievement-comparison/products",[SaleAchievementComparisonController::class,'getValidProducts'])->name('show_sales-achievement-comparison'); // TODO:permission is not implemented yet
Route::post("/sales-achievement-comparison/division-types",[SaleAchievementComparisonController::class,'getFilterDivisionTypes'])->name('show_sales-achievement-comparison'); // TODO:permission is not implemented yet
Route::post("/sales-achievement-comparison",[SaleAchievementComparisonController::class,'getReportData'])->name('show_sales-achievement-comparison'); // TODO:permission is not implemented yet

// Branch Sales Details Report
Route::post('/branch-sales-details-report', [BranchSaleDetailsReportController::class, 'filter'])
    ->name('show_reports_branch_sales_details');
Route::post('/branch-sales-details-report/download', [BranchSaleDetailsReportController::class, 'downloadExcel'])->name(''); // TODO:permission is not implemented yet

// Sales Summery Report
Route::get('/lines-sales-summery', [SalesSummeryReportController::class, 'getLines'])->name('');
Route::post('/line-data-sales-summery', [SalesSummeryReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/sales-summery-report', [SalesSummeryReportController::class, 'filter'])->name('show_reports_sales_summary');

// Sales Per distributor Report
Route::get('/lines-sales-distributor', [SalesPerDistributorReportController::class, 'getLines'])->name('');
Route::post('/line-data-sales-distributor', [SalesPerDistributorReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/sales-distributor-report', [SalesPerDistributorReportController::class, 'filter'])->name('show_reports_sales_per_distributors');


// Sales Achievement Report
Route::post('/lines-sales-achievement', [SalesAchievementReportController::class, 'getLines'])->name('');
Route::post('/line-data-sales-achievement', [SalesAchievementReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-product-data', [SalesAchievementReportController::class, 'getProductData'])->name(''); //TODO:permission is not implemented yet
Route::post('/sales-achievement-report', [SalesAchievementReportController::class, 'filter'])->name('show_reports_sales_achievement');
Route::post('/sales-achievement-perbrand-report', [SalesAchievementPerBrandController::class, 'filter'])->name('show_reports_sales_achievement');

// Target Summary Report
Route::get('/lines-targets-summary', [TargetSummaryReportController::class, 'getLines'])->name('');
Route::post('/line-data-targets-summary', [TargetSummaryReportController::class, 'getLineData'])->name('show_reports_target_summary'); //TODO:permission is not implemented yet
Route::post('/targets-summary-report', [TargetSummaryReportController::class, 'filter'])->name('show_reports_target_summary');

// Target Details Report
Route::post('/targets-details-report', [TargetDetailsReportController::class, 'filter'])->name('show_reports_target_details');


// Sales Employees Report
Route::get('/lines-sales-employees', [SalesEmployeesReportController::class, 'getLines'])->name('');
Route::post('/line-data-sales-employees', [SalesEmployeesReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/sales-employees-report', [SalesEmployeesReportController::class, 'filter'])->name('show_reports_sales_employees');


// Sales Summary Unified Report
Route::get('/lines-sales-summary-unified', [SalesSummaryUnifiedReportController::class, 'getLines'])->name('');
Route::post('/line-data-sales-summary-unified', [SalesSummaryUnifiedReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/sales-summary-unified-report', [SalesSummaryUnifiedReportController::class, 'filter'])->name('show_reports_sales_summary_unified');

// Sales incentive Calculation Report
Route::post('/lines-sales-incentive', [SalesIncentiveCalculationReportController::class, 'getLines'])->name('');
Route::post('/line-data-sales-incentive', [SalesIncentiveCalculationReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/sales-incentive-report', [SalesIncentiveCalculationReportController::class, 'filter'])->name('show_reports_sales_incentive');
Route::post('/sales-incentive-perbrand-report', [SalesAchievementPerBrandController::class, 'filter'])->name('show_reports_sales_incentive');

// Start Point Report
Route::get('/lines-sales-summery', [SalesStartPointController::class, 'getLines'])->name('');
Route::post('/line-data-start-point', [SalesStartPointController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/sales-start-point-report', [SalesStartPointController::class, 'filter'])->name('show_reports_start_point');


// Product Frequency Report
Route::post('/product-frequency', [ProductFrequencyReportController::class, 'filter'])->name('show_reports_product_frequency');
Route::post('show-product-frequency', [ProductFrequencyReportController::class, 'showData'])->name('');

// Product Messages Report
Route::post('/product-messages-report/{line}', [ProductMessagesReportController::class, 'filter'])->name('show_reports_product_frequency');
Route::post('show-product-messages-report', [ProductMessagesReportController::class, 'showData'])->name('');

// Speciality Statistics Report
Route::post('/speciality-statistics/{line}', [VisitSpecialitiesStatsticsReportController::class, 'filter'])->name('show_reports_speciality_statistics');
Route::post('show-speciality-statistics', [VisitSpecialitiesStatsticsReportController::class, 'showData'])->name('');

// Doctor Tracing Report
Route::post('/get-doctors', [DoctorTracingReportController::class, 'getDoctors'])->name('');
Route::post('/doctor-tracing/{line}', [DoctorTracingReportController::class, 'filter'])->name('show_reports_doctor_tracing');
Route::post('show-doctor-tracing', [DoctorTracingReportController::class, 'showData'])->name('');

// Customer Visit Report
Route::post('/customer-visits', [CustomerVisitsReportController::class, 'filter'])->name('show_reports_customer_visits');
Route::post('show-customer-visits', [CustomerVisitsReportController::class, 'showData'])->name('');

// List Report
Route::get('/accountsLines', [ListReportController::class, 'getLines'])->name('');
Route::get('/getDataLineReport/{line}', [ListReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/list-report', [ListReportController::class, 'filter'])->name('show_reports_list_report');
Route::post('/list-report-action', [ListReportController::class, 'action'])->name('show_reports_list_report');
Route::post('/reset-location', [ListReportController::class, 'resetAccountLocation'])->name('');
Route::post('/account-location', [ListReportController::class, 'accountLocation'])->name('show_reports_list_report');

//Plan Report
Route::post('/visitsReport', [VisitsReportController::class, 'getLineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/filterReport', [VisitsReportController::class, 'filterReport'])->name('show_reports_overall_visits');
Route::post('/visit-details', [VisitsReportController::class, 'visitDetails'])->name('show_reports_overall_visits');
Route::post('/visit-location', [VisitsReportController::class, 'visitLocation'])->name('show_reports_overall_visits');
Route::post('/delete/plans/', [VisitsReportController::class, 'deletePlans'])->name('delete_plan_visits');
Route::post('/delete/plan/ow', [VisitsReportController::class, 'deleteOwPlans'])->name('delete_ow_plan_visits');

//Employee Route Report
Route::get('employee-route', [EmployeeRouteController::class, 'index'])->name(''); //TODO:permission is not implemented yet
Route::get('employee-route/{line}', [EmployeeRouteController::class, 'getLineEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('employee-routes', [EmployeeRouteController::class, 'employeeRoute'])->name(''); //TODO:permission is not implemented yet

// Team Locations Report
Route::get('team-locations', [TeamLocationsController::class, 'index'])->name(''); //TODO:permission is not implemented yet
Route::get('team-locations/{line_id}', [TeamLocationsController::class, 'getLineDivisions'])->name(''); //TODO:permission is not implemented yet

// Log Activity Report
Route::get('/get-data-activity', [LogActivityController::class, 'getData'])->name(''); //TODO:permission is not implemented yet
Route::post('/logActivityReport', [LogActivityController::class, 'logActivityReport'])->name('show_reports_log_activity');

// Coaching Statistics Report

Route::get('/coaching/report/{line}', [CoachingHeaderController::class, 'getLineData'])->name('');
Route::post('/coaching/statistics-report/', [CoachingHeaderController::class, 'statisticsReport'])->name('show_reports_coaching_statistics');


// Training Categories Report
Route::post('/get-line-data-training-reports', [TrainingCategoryReportController::class, 'getLineData'])->name('show_reports_question_quiz');
Route::post('/training-categories/{line}', [TrainingCategoryReportController::class, 'filter'])->name('show_reports_question_quiz');
Route::post('show-quiz-question', [TrainingCategoryReportController::class, 'showData'])->name('');

// Quiz Result Summary Report
Route::post('/get-line-data-quiz-result', [QuizResultSuumaryReportController::class, 'getLineData'])->name('show_reports_quiz_summery');
Route::post('/quiz-results/{line}', [QuizResultSuumaryReportController::class, 'filter'])->name('show_reports_quiz_summery');
Route::post('show-quiz-results', [QuizResultSuumaryReportController::class, 'showDataNoOfQuizzes'])->name('');
Route::post('show-quiz-results-for-category', [QuizResultSuumaryReportController::class, 'showDataCategoryQuestions'])->name('');

// frequency Report

Route::get('/frequency-report', [FrequencyReportController::class, 'lines'])->name('');
Route::get('/frequency-report-line-data/{line}', [FrequencyReportController::class, 'getLineData'])->name('');
Route::post('/frequency-report', [FrequencyReportController::class, 'filter'])->name('show_reports_frequency');
Route::post('show-frequency-data', [FrequencyReportController::class, 'showData'])->name('');

// Frequency Classic
Route::post('/classic-view-data', [FrequencyReportClassicViewController::class, 'getData'])->name('');
Route::post('/frequency-report-classic', [FrequencyReportClassicViewController::class, 'filter'])->name('');
Route::post('show-frequency-data-classic', [FrequencyReportClassicViewController::class, 'showData'])->name('');

// Frequency Details
Route::post('/frequency-report-details', [FrequencyDetailsViewController::class, 'filter'])->name('');
Route::post('show-frequency-data-details', [FrequencyDetailsViewController::class, 'showData'])->name('');


// Out of location
Route::post('/out-of-location', [OutOfLocationReportController::class, 'filter'])->name('show_reports_out_of_locations');

// Employee Tracking
Route::post('/employee-tracking', [EmployeeTrackingReportController::class, 'filter'])->name('');
Route::post('/employee-tracking-excel', [EmployeeTrackingReportController::class, 'excel'])->name('');

// Employee Kpis
Route::post('/get-employee-positions', [KpisReportController::class, 'getUsersUsingPositions'])->name('');
Route::post('/employee-kpis', [KpisReportController::class, 'filter'])->name('show_reports_employee_kpis');
Route::post('/advanced-employee-kpis', [PostKpisReportController::class, 'filter'])->name('show_reports_employee_kpis');


// Working Hours
Route::post('/working-hours', [WorkingHoursReportController::class, 'filter'])->name('show_reports_working_hours');
// Employee performance
Route::post('/employee-performance', [EmployeePerformanceReportController::class, 'filter'])->name('');
// Employee Policy
Route::post('/get-roles', [KpisPolicyReportController::class, 'getRoles'])->name('');
Route::post('/kpis-policy', [KpisPolicyReportController::class, 'filter'])->name('');

// GPS Visits
// Route::post('/gps-visits', [OutOfLocationReportController::class, 'filter'])->name('');
Route::post('/gps-visits', [VisitsReportController::class, 'gpsVisitFilterReport'])->name('show_gps_visits');






// Expense Route
Route::get('/get-line-data/{line}', [ExpenseReportController::class, 'getLineData'])->name('');
Route::post('/expense-report', [ExpenseReportController::class, 'filter'])->name('show_reports_expense_details');

// Expense Statistics

Route::get('/get-line-data-product-frequency/{line}', [ProductFrequencyReportController::class, 'getLineData'])->name('');
Route::get('/get-line-data-reports/{line}', [ExpenseStatisticsReportController::class, 'getLineData'])->name('');
Route::post('/expense-statistics', [ExpenseStatisticsReportController::class, 'filter'])->name('show_reports_expense_statistics');
Route::get('/edit-expense-statistics/{detail}', [ExpenseStatisticsReportController::class, 'getExpenseDetails'])->name('edit_expenses');
Route::put('/expense-stats/{detail}', [ExpenseStatisticsReportController::class, 'updateDetails'])->name('edit_expenses');
Route::post('show-expense-statistics', [ExpenseStatisticsReportController::class, 'showData'])->name('');
Route::delete('expense-stats/{expenseDetail}', [ExpenseStatisticsReportController::class, 'deleteExpenseDetail'])->name('');
Route::post('/show-edited-expense-users', [ExpenseStatisticsReportController::class, 'editedUsers'])->name(''); //TODO:permission is not implemented yet
Route::get('/edited-expense-users-notes/{detail}', [ExpenseStatisticsReportController::class, 'editedUsersNotes'])->name(''); //TODO:permission is not implemented yet
Route::post('/approve-expense', [ExpenseStatisticsReportController::class, 'ApproveExpenses'])->name(''); //TODO:permission is not implemented yet
Route::post('/disapprove-expense', [ExpenseStatisticsReportController::class, 'disApproveExpenses'])->name(''); //TODO:permission is not implemented yet
Route::post('/show-approvals-per-expense', [ExpenseStatisticsReportController::class, 'showExpensesApprovals'])->name(''); //TODO:permission is not implemented yet

// Expense Statistics V2.0

Route::post('/get-users-stats', [ExpenseStatisticsReportV2Controller::class, 'getUsers'])->name('');
Route::post('/get-lines-stats', [ExpenseStatisticsReportV2Controller::class, 'lines'])->name('');
Route::post('/get-lines-stats-data', [ExpenseStatisticsReportV2Controller::class, 'getLineData'])->name('');
Route::post('/second-version-expense-statistics', [ExpenseStatisticsReportV2Controller::class, 'filter'])->name('show_reports_expense_statistics_v2');
Route::get('/edit-expense-statistics-second-version/{detail}', [ExpenseStatisticsReportV2Controller::class, 'getExpenseDetails'])->name('edit_expenses');
Route::put('/expense-stats-second-version/{detail}', [ExpenseStatisticsReportV2Controller::class, 'updateDetails'])->name('edit_expenses');
Route::post('show-expense-statistics-second-version', [ExpenseStatisticsReportV2Controller::class, 'showData'])->name('');
Route::post('show-expense-total_amount-second-version', [ExpenseStatisticsReportV2Controller::class, 'getTotalAmountExpenses'])->name('');
Route::delete('expense-stats-second-version/{expenseDetail}', [ExpenseStatisticsReportV2Controller::class, 'deleteExpenseDetail'])->name('');
Route::post('/show-edited-expense-users-second-version', [ExpenseStatisticsReportV2Controller::class, 'editedUsers'])->name(''); //TODO:permission is not implemented yet
Route::get('/edited-expense-users-notes-second-version/{detail}', [ExpenseStatisticsReportV2Controller::class, 'editedUsersNotes'])->name(''); //TODO:permission is not implemented yet
Route::post('/approve-expense-second-version', [ExpenseStatisticsReportV2Controller::class, 'ApproveExpenses'])->name(''); //TODO:permission is not implemented yet
Route::post('/disapprove-expense-second-version', [ExpenseStatisticsReportV2Controller::class, 'disApproveExpenses'])->name(''); //TODO:permission is not implemented yet
Route::post('/show-approvals-per-expense-second-version', [ExpenseStatisticsReportV2Controller::class, 'showExpensesApprovals'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-expense-approvals', [ExpenseStatisticsReportV2Controller::class, 'getApprovalUser'])->name(''); //TODO:permission is not implemented yet
Route::delete('/expense/approval/detail/{id}', [ExpenseStatisticsReportV2Controller::class, 'deleteApprovalUser'])->name(''); //TODO:permission is not implemented yet
Route::post('/update-user-flow', [ExpenseStatisticsReportV2Controller::class, 'updateApprovalUser'])->name(''); //TODO:permission is not implemented yet
Route::post('/print-expenses', [ExpenseStatisticsReportV2Controller::class, 'printExpenses'])->name(''); //TODO:permission is not implemented yet

// Material Statistics
// Route::post('/material-statistics/{line}', [MaterialStatisticsReportController::class, 'filter'])->name('show_reports_material_statistics');
// Route::get('/material-types', [MaterialStatisticsReportController::class, 'getMaterialTypes'])->name('');
// Route::post('show-material-statistics', [MaterialStatisticsReportController::class, 'showData'])->name('');
// Route::get('/material-report-lines',[MaterialStatisticsReportController::class,'getData'])->name('');
// Route::post('/material-report', [MaterialStatisticsReportController::class, 'filter'])->name('show_reports_commercial_details');


// Material Statistics

Route::post('/Material-statistics/{line}', [MaterialStatisticsReportController::class, 'filter'])->name('show_reports_material_statistics');
Route::post('show-Material-statistics', [MaterialStatisticsReportController::class, 'showData'])->name('');

// Material Route
Route::get('/material-report-lines', [MaterialStatisticsReportController::class, 'getData'])->name('');
Route::get('/get-line-Material-data/{line}', [MaterialReportController::class, 'getLineData'])->name('');
Route::post('/material-report', [MaterialReportController::class, 'filter'])->name('show_reports_material_details');


// Commercial Statistics

Route::get('/commercial-report-lines', [CommercialStatisticsReportController::class, 'getData'])->name('');
Route::post('/commercial-statistics', [CommercialStatisticsReportController::class, 'filter'])->name('show_reports_commercial_statistics');
Route::post('show-commercial-statistics', [CommercialStatisticsReportController::class, 'showData'])->name('');
// Commercial Costs
Route::post('/get-line-commercial-cost', [CommercialCostsReportController::class, 'getLineData'])->name('');
Route::post('/commercial-costs', [CommercialCostsReportController::class, 'filter'])->name('show_reports_commercial_Costs');
Route::post('/show-data-commercial-costs', [CommercialCostsReportController::class, 'showData'])->name('show_reports_commercial_Costs');


// Commercial End Payments
Route::post('/end-payments', [EndCommercialPaymentsReportController::class, 'filter'])->name('show_reports_end_commercial_payments');
Route::post('/end-commercial-payments', [EndCommercialPaymentsReportController::class, 'save'])->name('show_reports_end_commercial_payments');
Route::post('/view-payments', [EndCommercialPaymentsReportController::class, 'viewFinancePayments'])->name('');
Route::post('/view-service-complete', [EndCommercialPaymentsReportController::class, 'viewFinanceServiceComplete'])->name('');
Route::post('/show/request/data/{id}', [EndCommercialPaymentsReportController::class, 'showRequest'])->name('');

// Finance Summary

Route::post('/finance-summary', [FinanceSummaryReportConroller::class, 'filter'])->name('show_reports_finance_summary');


// Commercial Route
Route::get('/commercial-report-lines', [CommercialStatisticsReportController::class, 'getData'])->name('');
Route::post('/get-line-commercial-data', [CommercialReportController::class, 'getLineData'])->name('');
Route::get('/get-line-request-data/{line}', [CommercialDataController::class, 'getLineData'])->name('');
Route::post('/commercial-report', [CommercialReportController::class, 'filter'])->name('show_reports_commercial_details');
Route::post('/save-archived', [CommercialReportController::class, 'saveArchived'])->name('create_commercial_archived');

// Custody Report

Route::get('/get-lines-custody', [CustodyReportController::class, 'getLines'])->name('');
Route::post('/custody-report-data', [CustodyReportController::class, 'getLineData'])->name('');
Route::post('/custody-report', [CustodyReportController::class, 'filter'])->name('show_reports_custody');
Route::post('/show-custody', [CustodyReportController::class, 'showData'])->name('show_reports_custody');
Route::post('/custody-report-excel', [CustodyReportController::class, 'filterExcel'])->name('show_reports_custody');

// Commercial Bills
Route::post('/commercial-bill-approvals', [CommercialBillsReportController::class, 'approvals'])->name('show_reports_commercial_bills');
Route::post('/show-commercial-bills', [CommercialBillsReportController::class, 'index'])->name('show_reports_commercial_bills');
Route::get('/commercial-bills/index', [CommercialBillsReportController::class, 'getData'])->name('');
Route::post('/commercial-bills-export', [CommercialBillsReportController::class, 'exportExcel'])->name('');
Route::get('/bills/{id}/requests', [CommercialBillsReportController::class, 'show'])->name('');
Route::put('/bills/{id}/requests', [CommercialBillsReportController::class, 'update'])->name('');
Route::delete('/bills/{id}/requests', [CommercialBillsReportController::class, 'delete'])->name('');
Route::post('/commercial-bills', [CommercialBillsReportController::class, 'filter'])->name('show_reports_commercial_bills');
Route::delete('/commercial-bills/{bill}', [CommercialBillsReportController::class, 'destroy'])->name('');
Route::post('/save-commercial-bills', [CommercialBillsReportController::class, 'store'])->name('show_reports_commercial_bills');


// Budget Details Report
Route::post('/budget-report', [BudgetDetailsReportController::class, 'filter'])->name('show_reports_budget_details');

// Budget Consumption Report

Route::post('/budget-consumptions', [BudgetConsumptionReportController::class, 'filter'])->name('show_reports_budget_consumptions');

Route::post('/budget-analysis', [BudgetAnalysisController::class, 'filter'])->name('show_reports_budget_analysis');
Route::post('/show-budget-analysis', [BudgetAnalysisController::class, 'showData'])->name('show_reports_budget_analysis');


// Material Costs
Route::get('/material-costs-lines', [MaterialCostReportController::class, 'getLines'])->name('');
Route::post('/get-line-material-cost', [MaterialCostReportController::class, 'getLineData'])->name('');
Route::post('/material-costs', [MaterialCostReportController::class, 'filter'])->name('show_reports_material_costs');


// Order Requests
Route::get('/order-request-bricks', [OrderRequestReportController::class, 'getBricks'])->name('');
Route::post('/order-requests', [OrderRequestReportController::class, 'filter'])->name('');
Route::post('/save-order-requests', [OrderRequestReportController::class, 'save'])->name('');

//Finance Order Request
Route::post('/finance-orders', [FinanceOrderReportController::class, 'filter'])->name('');
Route::post('/save-finance-orders', [FinanceOrderReportController::class, 'save'])->name('');


// doctor profiling Report
Route::post('/doctor-profiling-report', [DoctorProfilingReportController::class, 'report_filter'])->name('');

// notifications Report
Route::post('/notifications-report', [NotificationReportController::class, 'report_filter'])->name('');
Route::get('/notifications-types', [NotificationReportController::class, 'get_types'])->name('');


// linked-pharmacies Report
Route::get('/get-lines-data/{line}', [LinkedPharmaciesController::class, 'getLineData'])->name('');
Route::post('/linked-pharmacies-report', [LinkedPharmaciesReportController::class, 'find'])->name('');
Route::post('/show-linked-pharmacies', [LinkedPharmaciesReportController::class, 'showData'])->name('');
Route::post('/linked-pharmacies-details-report', [LinkedPharmaciesReportController::class, 'findDetails'])->name('');
Route::post('/linked-line-data-report', [LinkedPharmaciesReportController::class, 'getLineData'])->name('');

Route::post('/structure-diagram-report/{line}', [StructureDiagramReportController::class, 'filteration'])->name('');
Route::get('/structure-diagram-report/{id}', [StructureDiagramReportController::class, 'index'])->name('');
Route::post('/ceiling-report', [CeilingReportController::class, 'index'])->name('');
Route::post('/ceiling-report/recalc', [CeilingReportController::class, 'recalc'])->name('');

// Account Location Comparison Report
Route::post('/account-location-comparison', [AccountLocationComparisonController::class, 'generateReport'])->name('show_reports_account_location_comparison');
Route::get('/account-location-comparison/info', [AccountLocationComparisonController::class, 'getReportInfo'])->name('account_location_comparison_info');

Route::post('/summery-view-report', [HelicopterViewReportController::class, 'filter'])->name('show_reports_summery_report');
Route::get('/summery-view-report/lines', [HelicopterViewReportController::class, 'getLines'])->name('');
// general routes
Route::post('/get-lines-with-dates', [LinesDataApiController::class, 'lines'])->name('');
Route::post('/get-lines-data-with-dates', [LinesDataApiController::class, 'getLineData'])->name('');
