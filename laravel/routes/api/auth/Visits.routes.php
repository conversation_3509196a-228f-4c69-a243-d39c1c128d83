<?php

use App\Http\Controllers\AccountTypeDistanceController;
use App\Http\Controllers\ActualDoubleFeedbackController;
use App\Http\Controllers\DoublePlanController;
use App\Http\Controllers\ActualVisitsController;
use App\Http\Controllers\ActualVisitSettingController;
use App\Http\Controllers\AutomaticPlanController;
use App\Http\Controllers\AvRequiredInputController;
use App\Http\Controllers\CallRateController;
use App\Http\Controllers\ChangePlanController;
use App\Http\Controllers\DailyViewDataController;
use App\Http\Controllers\DisapprovalReasonController;
use App\Http\Controllers\FavouriteListPerUser;
use App\Http\Controllers\FavouriteListPerUserController;
use App\Http\Controllers\GiveawayController;
use App\Http\Controllers\ManagerController;
use App\Http\Controllers\OfficeWorkTypeController;
use App\Http\Controllers\OtherSettingController;
use App\Http\Controllers\OWActualVisitController;
use App\Http\Controllers\OWPlanVisitController;
use App\Http\Controllers\PlanApprovalDaySettingController;
use App\Http\Controllers\PlanApprovalSettingController;
use App\Http\Controllers\PlanCalendarViewController;
use App\Http\Controllers\PlanLevelController;
use App\Http\Controllers\PlanReasonController;
use App\Http\Controllers\PlanSettingController;
use App\Http\Controllers\PlanVisitColumnController;
use App\Http\Controllers\PlanVisitLimitController;
use App\Http\Controllers\PvController;
use App\Http\Controllers\StartPointController;
use App\Http\Controllers\UnderTimeController;
use App\Http\Controllers\UserActualStartDayController;
use App\Http\Controllers\UserStartPlanController;
use App\Http\Controllers\VisitFeedbackController;
use App\Models\ActualDoubleFeedback;
use App\Models\PlanVisitLimit;
use Illuminate\Support\Facades\Route;



//resource plans
Route::post('/plans/index', [DailyViewDataController::class, 'index'])->name('show_all_plan_visits');
// Route::post('/plans',[PlanVisitsController::class,'store'])->name('create_plan_visits');
// Route::get('/plans/create',[PlanVisitsController::class,'create'])->name('create_plan_visits');
Route::get('/plans/{id}', [DailyViewDataController::class, 'show'])->name('show_single_plan_visits');
Route::put('/plans/{id}', [DailyViewDataController::class, 'update'])->name('edit_plan_visits');

Route::post('/get-linked-pharmacies', [DailyViewDataController::class, 'getLinkedPharmacies'])->name('show_all_linked_pharmacies');


Route::post('/getLineDivisionBricks', [DailyViewDataController::class, 'getLineDivisionBricks'])->name(''); //TODO:permission is not implemented yet
Route::post('/division-from-brick', [DailyViewDataController::class, 'getDivisionFromBrick'])->name(''); //TODO:permission is not implemented yet

Route::get('/plan-visit-policies', [DailyViewDataController::class, 'policies'])->name('');


// Chabge Plan

Route::get('/change-plans-dates', [ChangePlanController::class, 'getMinDate'])->name('');
Route::post('/change-plans', [ChangePlanController::class, 'save'])->name('');



// Clarification plan
Route::post('/plan-clarification', [DailyViewDataController::class, 'saveClarification'])->name('');



Route::post('/get-plans', [AutomaticPlanController::class, 'getPlans'])->name(''); //TODO:permission is not implemented yet
Route::post('/automatic-plan', [AutomaticPlanController::class, 'store'])->name(''); //TODO:permission is not implemented yet

//resource OfficeWorkPlanVisits
Route::get('/owplanvisits', [OWPlanVisitController::class, 'index'])->name('show_all_ow_plan_visits');
Route::post('/owplanvisits', [OWPlanVisitController::class, 'store'])->name('create_ow_plan_visits');
Route::get('/owplanvisits/create', [OWPlanVisitController::class, 'create'])->name('create_ow_plan_visits');
Route::get('/owplanvisits/{id}/edit', [OWPlanVisitController::class, 'edit'])->name('edit_ow_plan_visits');
Route::get('/owplanvisits/{id}', [OWPlanVisitController::class, 'show'])->name('show_single_ow_plan_visits');
Route::put('/owplanvisits/{id}', [OWPlanVisitController::class, 'update'])->name('edit_ow_plan_visits');
Route::delete('/ow-plan-visits/{owPlanVisit}', [OWPlanVisitController::class, 'destroy'])->name('delete_ow_plan_visits');

Route::get('getOWPlanVisits/{day?}', [OWPlanVisitController::class, 'getOWPlanVisits'])->name(''); //TODO:permission is not implemented yet
Route::post('/owplanvisits/getOwPlanSchedule', [OWPlanVisitController::class, 'getOwPlanSchedule'])->name(''); //TODO:permission is not implemented yet

Route::get('/export-ow-plans', [OWPlanVisitController::class, 'exportOwPlans'])->name('export_xlsx_ow_plan_visits');
Route::get('/export-ow-plans-csv', [OWPlanVisitController::class, 'exportOwPlansCsv'])->name('export_csv_ow_plan_visits');
Route::get('/export-ow-plans-pdf', [OWPlanVisitController::class, 'exportOwPlanspdf'])->name('export_pdf_ow_plan_visits');
Route::post('/send-mail-ow-plans', [OWPlanVisitController::class, 'sendOwPlansmail'])->name('export_email_ow_plan_visits');

//resource double plan
Route::get('/double_plan', [DoublePlanController::class, 'index'])->name('show_all_double_plans');
Route::post('/double_plan', [DoublePlanController::class, 'save'])->name('create_double_plans');
Route::get('getdirectemployees/{line_id}', [DoublePlanController::class, 'getEmployees'])->name(''); //TODO:permission is not implemented yet
Route::post('get_plan_visits', [DoublePlanController::class, 'getPlanVisits'])->name(''); //TODO:permission is not implemented yet


//resource actual visits
Route::post('/actual_visits/index', [ActualVisitsController::class, 'index'])->name('show_all_actual_visits');
Route::post('/actual_visits', [ActualVisitsController::class, 'store'])->name('create_actual_visits');
Route::post('/actual/visits/accounts', [ActualVisitsController::class, 'storePerAccount'])->name('create_actual_visits');
Route::get('/actual_visits/create', [ActualVisitsController::class, 'create'])->name('create_actual_visits');
Route::get('/actual_visits/{id}/edit', [ActualVisitsController::class, 'edit'])->name('edit_actual_visits');
Route::get('/actual_visits/{id}', [ActualVisitsController::class, 'show'])->name('show_single_actual_visits');
Route::put('/actual_visits/{id}', [ActualVisitsController::class, 'update'])->name('edit_actual_visits');
Route::delete('/actual_visits/{id}', [ActualVisitsController::class, 'destroy'])->name('delete_actual_visits');
Route::get('/actual-visit-policies', [ActualVisitsController::class, 'policies'])->name('');
Route::get('/get-visit-another-types/{id}', [ActualVisitsController::class, 'getDoubleVisitTypes'])->name('');
Route::post('/get-message-map', [ActualVisitsController::class, 'getMapMessage'])->name('');

Route::get('/get-product-data/{id}', [ActualVisitsController::class, 'getProductData'])->name('');
Route::get('/get-slides/{presentation}', [ActualVisitsController::class, 'getSlides'])->name('');
Route::get('/get-doctor-data/{doctor}', [ActualVisitsController::class, 'getDoctorData'])->name('');
Route::post('/save-followups', [ActualVisitsController::class, 'saveFollowUps'])->name('');
Route::post('/get-account-location', [ActualVisitsController::class, 'accountLocation'])->name('');

Route::post('get-products', [ActualVisitsController::class, 'getProducts'])->name(''); //TODO:permission is not implemented yet

Route::post('/getAccounts', [ActualVisitsController::class, 'getAccountsRequest'])->name(''); //TODO:permission is not implemented yet
Route::get('getDoctors/{line}/{account_id}/{div_id}', [ActualVisitsController::class, 'getDoctors'])->name(''); //TODO:permission is not implemented yet
Route::get('actual_visits/create_plan_actual_visit/{id}', [ActualVisitsController::class, 'createPlanActualVisit'])->name('create_plan_actual_visits');

Route::get('/exportactuals', [ActualVisitsController::class, 'exportActuals'])->name('export_xlsx_actual_visits');
Route::get('/exportactuals-csv', [ActualVisitsController::class, 'exportActualsCsv'])->name('export_csv_actual_visits');
Route::get('/exportactuals-pdf', [ActualVisitsController::class, 'exportActualspdf'])->name('export_pdf_actual_visits');
Route::post('/send-mailactuals', [ActualVisitsController::class, 'sendActualsmail'])->name('export_email_actual_visits');
Route::post('/import-visits', [ActualVisitsController::class, 'importVisits'])->name('edit_actual_visits');

Route::get('pv-data', [PvController::class, 'getData'])->name('');
Route::post('save-pv', [PvController::class, 'store'])->name('create_pv_requests');
Route::get('pv/{pv}', [PvController::class, 'show'])->name('show_single_pv_requests');
Route::put('pv/{pv}', [PvController::class, 'update'])->name('edit_pv_requests');

// double visit feedback

Route::post('/double-feedback/{doubleVisit}', [ActualDoubleFeedbackController::class, 'storeFeedback'])->name('create_actual_double_feedbacks');
Route::get('/double-feedback/{doubleFeedback}', [ActualDoubleFeedbackController::class, 'showInVisit'])->name('show_all_actual_double_feedbacks');
Route::put('/double-feedback/{doubleFeedback}', [ActualDoubleFeedbackController::class, 'updateInVisit'])->name('edit_actual_double_feedbacks');
Route::delete('/double-feedback/{doubleFeedback}', [ActualDoubleFeedbackController::class, 'destroyInVisit'])->name('delete_actual_double_feedbacks');


// Double visit Approval
Route::post('/save-double-location', [ActualVisitsController::class, 'saveDoubleLocation'])->name('');


//resource OfficeWorkActualVisits
Route::post('/owactualvisits/index', [OWActualVisitController::class, 'index'])->name('show_all_ow_actual_visits');
Route::post('/owactualvisits', [OWActualVisitController::class, 'store'])->name('create_ow_actual_visits');
Route::post('/ow-plan-actual/{id}', [OWActualVisitController::class, 'convertOWToActual'])->name('create_ow_actual_visits');
Route::get('/owactualvisits/create', [OWActualVisitController::class, 'create'])->name('create_ow_actual_visits');
Route::get('/owactualvisits/{id}/edit', [OWActualVisitController::class, 'edit'])->name('edit_ow_actual_visits');
Route::get('/owactualvisits/{id}', [OWActualVisitController::class, 'show'])->name('show_single_ow_actual_visits');
Route::put('/owactualvisits/{id}', [OWActualVisitController::class, 'update'])->name('edit_ow_actual_visits');
Route::delete('/owactualvisits/{id}', [OWActualVisitController::class, 'destroy'])->name('delete_ow_actual_visits');

Route::get('/export-ow-actuals', [OWActualVisitController::class, 'exportOwActuals'])->name('export_xlsx_ow_actual_visits');
Route::get('/export-ow-actuals-csv', [OWActualVisitController::class, 'exportOwActualsCsv'])->name('export_csv_ow_actual_visits');
Route::get('/export-ow-actuals-pdf', [OWActualVisitController::class, 'exportOwActualspdf'])->name('export_pdf_ow_actual_visits');
Route::post('/send-mail-ow-actuals', [OWActualVisitController::class, 'sendOwActualsmail'])->name('export_email_ow_actual_visits');

//resource plan settings
Route::get('/plansettings', [PlanSettingController::class, 'index'])->name('show_all_plan_visit_settings');
Route::post('/plansettings', [PlanSettingController::class, 'store'])->name('create_plan_visit_settings');
Route::get('/plansettings/create', [PlanSettingController::class, 'create'])->name('create_plan_visit_settings');
Route::get('/plansettings/{id}/edit', [PlanSettingController::class, 'edit'])->name('edit_plan_visit_settings');
Route::get('/plansettings/key/{key}', [PlanSettingController::class, 'ByKey'])->name(''); //Todo permission is not implemented
Route::get('/plansettings/{id}', [PlanSettingController::class, 'show'])->name('show_single_plan_visit_settings');
Route::put('/plansettings/{id}', [PlanSettingController::class, 'update'])->name('edit_plan_visit_settings');


//resource Plan Approval Day Settings
Route::get('/plan_approval_day_settings', [PlanApprovalDaySettingController::class, 'index'])->name('show_all_plan_approval_day_settings');
Route::post('/plan_approval_day_settings', [PlanApprovalDaySettingController::class, 'store'])->name('create_plan_approval_day_settings');
Route::get('/plan_approval_day_settings/create', [PlanApprovalDaySettingController::class, 'create'])->name('create_plan_approval_day_settings');
Route::get('/plan_approval_day_settings/{id}/edit', [PlanApprovalDaySettingController::class, 'edit'])->name('edit_plan_approval_day_settings');
Route::get('/plan_approval_day_settings/{id}', [PlanApprovalDaySettingController::class, 'show'])->name('show_single_plan_approval_day_settings');
Route::put('/plan_approval_day_settings/{id}', [PlanApprovalDaySettingController::class, 'update'])->name('edit_plan_approval_day_settings');
Route::delete('/plan_approval_day_settings/{id}', [PlanApprovalDaySettingController::class, 'destroy'])->name('delete_plan_approval_day_settings');



// //resource Plan Approval Settings
// Route::get('/planapprovalsettings',[PlanApprovalSettingController::class,'index'])->name('show_all_plan_approval_settings');
// Route::post('/planapprovalsettings',[PlanApprovalSettingController::class,'store'])->name('create_plan_approval_settings');
// Route::get('/planapprovalsettings/create',[PlanApprovalSettingController::class,'create'])->name('create_plan_approval_settings');
// Route::get('/planapprovalsettings/{id}/edit',[PlanApprovalSettingController::class,'edit'])->name('edit_plan_approval_settings');
// Route::get('/planapprovalsettings/{id}',[PlanApprovalSettingController::class,'show'])->name('show_single_plan_approval_settings');
// Route::put('/planapprovalsettings/{id}',[PlanApprovalSettingController::class,'update'])->name('edit_plan_approval_settings');
// Route::delete('/planapprovalsettings/{id}',[PlanApprovalSettingController::class,'destroy'])->name('delete_plan_approval_settings');

// Route::get('/getLineLevels/{line_id}', [PlanApprovalSettingController::class,'getLineLevels'])->name('');
// Route::get('/getParentDivisions/{line_id}/{div_id}', [PlanApprovalSettingController::class,'getParentDivisions'])->name('');


//resource Actual Visit Settings
Route::get('/actualvisitsettings', [ActualVisitSettingController::class, 'index'])->name('show_all_actual_visit_settings');
Route::post('/actualvisitsettings', [ActualVisitSettingController::class, 'store'])->name('create_actual_visit_settings');
Route::get('/actualvisitsettings/create', [ActualVisitSettingController::class, 'create'])->name('create_actual_visit_settings');
Route::get('/actualvisitsettings/{id}/edit', [ActualVisitSettingController::class, 'edit'])->name('edit_actual_visit_settings');
Route::get('/actualvisitsettings/{id}', [ActualVisitSettingController::class, 'show'])->name('show_single_actual_visit_settings');
Route::put('/actualvisitsettings/{id}', [ActualVisitSettingController::class, 'update'])->name('edit_actual_visit_settings');

//PlanVisitColumns
Route::get('/plan_visit_columns', [PlanVisitColumnController::class, 'getColumns'])->name('show_all_plan_visit_columns');
Route::post('/addColumns', [PlanVisitColumnController::class, 'addColumns'])->name('edit_plan_visit_columns');


//resource OfficeWorkTypes
Route::get('/officeworktypes', [OfficeWorkTypeController::class, 'index'])->name('show_all_office_work_types');
Route::post('/officeworktypes', [OfficeWorkTypeController::class, 'store'])->name('create_office_work_types');
Route::get('/officeworktypes/create', [OfficeWorkTypeController::class, 'create'])->name('create_office_work_types');
Route::get('/officeworktypes/{id}/edit', [OfficeWorkTypeController::class, 'edit'])->name('edit_office_work_types');
Route::get('/officeworktypes/{id}', [OfficeWorkTypeController::class, 'show'])->name('show_single_office_work_types');
Route::put('/officeworktypes/{id}', [OfficeWorkTypeController::class, 'update'])->name('edit_office_work_types');
Route::delete('/officeworktypes/{id}', [OfficeWorkTypeController::class, 'destroy'])->name('delete_office_work_types');

//ActualVisitInputs
Route::get('/actual_visit_inputs', [AvRequiredInputController::class, 'getInputs'])->name('show_all_av_required_inputs');
Route::post('/addInputs', [AvRequiredInputController::class, 'addInputs'])->name('create_av_required_inputs');

//Actual distances per account type
Route::get('/type-distance', [AccountTypeDistanceController::class, 'index'])->name('show_all_account_type_distances');
Route::post('/type-distance', [AccountTypeDistanceController::class, 'store'])->name('create_account_type_distances');
Route::get('/type-distance/{id}', [AccountTypeDistanceController::class, 'show'])->name('show_single_account_type_distances');
Route::put('/type-distance/{id}', [AccountTypeDistanceController::class, 'update'])->name('edit_account_type_distances');
Route::delete('/type-distance/{id}', [AccountTypeDistanceController::class, 'destroy'])->name('delete_account_type_distances');

//resource VisitFeedbacks
Route::get('/visitfeedbacks', [VisitFeedbackController::class, 'index'])->name('show_all_visit_feedbacks');
Route::post('/visitfeedbacks', [VisitFeedbackController::class, 'store'])->name('create_visit_feedbacks');
Route::get('/visitfeedbacks/create', [VisitFeedbackController::class, 'create'])->name('create_visit_feedbacks');
Route::get('/visitfeedbacks/{id}/edit', [VisitFeedbackController::class, 'edit'])->name('edit_visit_feedbacks');
Route::get('/visitfeedbacks/{id}', [VisitFeedbackController::class, 'show'])->name('show_single_visit_feedbacks');
Route::put('/visitfeedbacks/{id}', [VisitFeedbackController::class, 'update'])->name('edit_visit_feedbacks');
Route::delete('/visitfeedbacks/{id}', [VisitFeedbackController::class, 'destroy'])->name('delete_visit_feedbacks');


//resource Giveaways
Route::get('/giveaways', [GiveawayController::class, 'index'])->name('show_all_giveaways');
Route::post('/giveaways', [GiveawayController::class, 'store'])->name('create_giveaways');
Route::get('/giveaways/create', [GiveawayController::class, 'create'])->name('create_giveaways');
Route::get('/giveaways/{id}/edit', [GiveawayController::class, 'edit'])->name('edit_giveaways');
Route::get('/giveaways/{id}', [GiveawayController::class, 'show'])->name('show_single_giveaways');
Route::put('/giveaways/{id}', [GiveawayController::class, 'update'])->name('edit_giveaways');
Route::delete('/giveaways/{id}', [GiveawayController::class, 'destroy'])->name('delete_giveaways');

// sample quantity per division
Route::post('/giveaway-samples-list', [GiveawayController::class, 'samples'])->name('show_all_products');
Route::put('/giveaway-sample-qty', [GiveawayController::class, 'update_sample_qty'])->name('');


//resource Start Plan Day Users
Route::get('/startplanusers', [UserStartPlanController::class, 'index'])->name('show_all_start_plan_day_users');
Route::post('/startplanusers', [UserStartPlanController::class, 'store'])->name('create_start_plan_day_users');
Route::get('/startplanusers/create', [UserStartPlanController::class, 'create'])->name('create_start_plan_day_users');
Route::get('/startplanusers/{id}/edit', [UserStartPlanController::class, 'edit'])->name('edit_start_plan_day_users');
Route::get('/startplanusers/{id}', [UserStartPlanController::class, 'show'])->name('show_single_start_plan_day_users');
Route::put('/startplanusers/{id}', [UserStartPlanController::class, 'update'])->name('edit_start_plan_day_users');
Route::delete('/startplanusers/{id}', [UserStartPlanController::class, 'destroy'])->name('delete_start_plan_day_users');

Route::get('startplanusers/getLineLastLevelUsers/{line_id}', [UserStartPlanController::class, 'getLineLastLevelUsers'])->name(''); //TODO:permission is not implemented yet
Route::get('startplanusers/getLineDivisions/{line_id}', [UserStartPlanController::class, 'getLineDivisions'])->name(''); //TODO:permission is not implemented yet
Route::get('startplanusers/getDivisionUsers/{line_id}/{div_id}', [UserStartPlanController::class, 'getDivisionUsers'])->name(''); //TODO:permission is not implemented yet

//resource User Actual Start Day
Route::get('/user_actual_start_day', [UserActualStartDayController::class, 'index'])->name('show_all_start_actual_day_users');
Route::post('/user_actual_start_day', [UserActualStartDayController::class, 'store'])->name('create_start_actual_day_users');
Route::get('/user_actual_start_day/create', [UserActualStartDayController::class, 'create'])->name('create_start_actual_day_users');
Route::get('/user_actual_start_day/{id}/edit', [UserActualStartDayController::class, 'edit'])->name('edit_start_actual_day_users');
Route::get('/user_actual_start_day/{id}', [UserActualStartDayController::class, 'show'])->name('show_single_start_actual_day_users');
Route::put('/user_actual_start_day/{id}', [UserActualStartDayController::class, 'update'])->name('edit_start_actual_day_users');
Route::delete('/user_actual_start_day/{id}', [UserActualStartDayController::class, 'destroy'])->name('delete_start_actual_day_users');


// Favourite List Per User

Route::get('/fav-per-user', [FavouriteListPerUserController::class, 'index'])->name('show_all_start_actual_day_users');
Route::post('/fav-per-user', [FavouriteListPerUserController::class, 'store'])->name('create_start_actual_day_users');
Route::delete('/fav-per-user/{id}', [FavouriteListPerUserController::class, 'destroy'])->name('delete_start_actual_day_users');


//Managers
Route::get('/getManagers/{line_id}', [ManagerController::class, 'getManagers'])->name(''); //TODO:permission is not implemented yet
Route::get('/getUserManagers/{line_id}/{user_id}', [ManagerController::class, 'getUserManagers'])->name(''); //TODO:permission is not implemented yet

//resource Call Rate
Route::get('/callrates', [CallRateController::class, 'index'])->name('show_all_call_rates');
Route::post('/callrates', [CallRateController::class, 'store'])->name('create_call_rates');
Route::get('/callrates/create', [CallRateController::class, 'create'])->name('create_call_rates');
Route::get('/callrates/{id}/edit', [CallRateController::class, 'edit'])->name('edit_call_rates');
Route::get('/callrates/{id}', [CallRateController::class, 'show'])->name('show_single_call_rates');
Route::put('/callrates/{id}', [CallRateController::class, 'update'])->name('edit_call_rates');
Route::delete('/callrates/{id}', [CallRateController::class, 'destroy'])->name('delete_call_rates');
Route::get('/call-rate-replicate', [CallRateController::class, 'replicate'])->name('');

Route::get('/getLineDivisions/{line}', [CallRateController::class, 'getLineDivisions'])->name(''); //TODO:permission is not implemented yet

Route::post('/import-call-rates', [CallRateController::class, 'import'])->name('import_call_rates');
Route::post('/import-update-call-rates', [CallRateController::class, 'updateByImport'])->name('import_bulk_edit');
Route::get('/export-call-rates', [CallRateController::class, 'export'])->name('export_xlsx_call_rates');
Route::get('/export-call-rates-csv', [CallRateController::class, 'exportcsv'])->name('export_csv_call_rates');
Route::get('/export-call-rates-pdf', [CallRateController::class, 'exportpdf'])->name('export_pdf_call_rates');
Route::post('/send-mail-call-rates', [CallRateController::class, 'sendmail'])->name('export_email_call_rates');


//resource plan reasons
Route::get('/reasons', [DisapprovalReasonController::class, 'index'])->name('show_all_disapproval_reasons');
Route::post('/reasons', [DisapprovalReasonController::class, 'store'])->name('create_disapproval_reasons');
Route::get('/reasons/{id}', [DisapprovalReasonController::class, 'show'])->name('show_single_disapproval_reasons');
Route::put('/reasons/{id}', [DisapprovalReasonController::class, 'update'])->name('edit_disapproval_reasons');
Route::delete('/reasons/{id}', [DisapprovalReasonController::class, 'destroy'])->name('delete_disapproval_reasons');

Route::post('plan-reason', [PlanReasonController::class, 'store'])->name('create_plan_reasons');



// Daily Plan View

Route::get('/get-lines', [DailyViewDataController::class, 'lines'])->name(''); //TODO:permission is not implemented yet
Route::get('/get-line-division/{line}/{flag?}', [DailyViewDataController::class, 'lineData'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-line-division-bricks', [DailyViewDataController::class, 'bricks'])->name(''); //TODO:permission is not implemented yet
Route::post('/get-plan-schedule', [DailyViewDataController::class, 'getPlanSchedule'])->name(''); //TODO:permission is not implemented yet
Route::post('/calendar/get-plan-schedule', [PlanCalendarViewController::class, 'getPlanSchedule'])->name(''); //TODO:permission is not implemented yet
Route::post('/plans-daily', [DailyViewDataController::class, 'store'])->name('create_plan_visits');
Route::delete('/plans-daily/{id}', [DailyViewDataController::class, 'destroy'])->name('delete_plan_visits');

Route::get('/export-plans', [DailyViewDataController::class, 'exportPlans'])->name(''); //TODO:permission is not implemented yet
Route::get('/export-plans-csv', [DailyViewDataController::class, 'exportPlansCsv'])->name(''); //TODO:permission is not implemented yet
Route::get('/export-plans-pdf', [DailyViewDataController::class, 'exportpdf'])->name(''); //TODO:permission is not implemented yet
Route::post('/send-mail-plans', [DailyViewDataController::class, 'sendmail'])->name(''); //TODO:permission is not implemented yet



// Other Setting 
Route::get('/general-other-settings', [OtherSettingController::class, 'index'])->name('show_all_other_settings');
Route::get('/frequency-types/{id}', [OtherSettingController::class, 'frequencyTypes'])->name('show_all_other_settings');
Route::get('/other-settings/{id}', [OtherSettingController::class, 'show'])->name('show_single_other_settings');
Route::put('/other-settings/{id}', [OtherSettingController::class, 'update'])->name('edit_other_settings');

// Actual Visit double feedback
// Route::get('/actual-double-feedback',[ActualDoubleFeedbackController::class,'index'])->name('show_all_actual_double_feedbacks');
Route::get('/actual-double-feedback/lines', [ActualDoubleFeedbackController::class, 'getLines'])->name('');
Route::get('/actual-double-feedback/lines/{line}', [ActualDoubleFeedbackController::class, 'lineData'])->name('');
Route::post('/actual-double-feedback/visits', [ActualDoubleFeedbackController::class, 'getVisits'])->name('');
Route::post('/actual-double-feedback', [ActualDoubleFeedbackController::class, 'store'])->name('create_actual_double_feedbacks');
// Route::get('/actual-double-feedback/{id}',[ActualDoubleFeedbackController::class,'show'])->name('show_single_actual_double_feedbacks');
// Route::put('/actual-double-feedback/{id}',[ActualDoubleFeedbackController::class,'update'])->name('edit_actual_double_feedbacks');


// Start Point
Route::get('/get-month-days', [StartPointController::class, 'getMonths'])->name('');
Route::post('/save-start-points', [StartPointController::class, 'store'])->name('');


// Plan Limit
Route::get('/plan/limit', [PlanVisitLimitController::class, 'index'])->name('show_all_plan_limits');
Route::post('/plan/limit', [PlanVisitLimitController::class, 'store'])->name('create_plan_limits');
Route::get('/plan/limit/{id}', [PlanVisitLimitController::class, 'show'])->name('show_single_plan_limits');
Route::put('/plan/limit/{id}', [PlanVisitLimitController::class, 'update'])->name('edit_plan_limits');
Route::delete('/plan/limit/{id}', [PlanVisitLimitController::class, 'destroy'])->name('delete_plan_limits');


// plan level 
Route::get('/plan/level', [PlanLevelController::class, 'index'])->name('show_all_plan_levels');
Route::post('/plan/level', [PlanLevelController::class, 'store'])->name('create_plan_levels');
Route::get('/plan/level/{id}', [PlanLevelController::class, 'show'])->name('show_single_plan_levels');
Route::put('/plan/level/{id}', [PlanLevelController::class, 'update'])->name('edit_plan_levels');
Route::delete('/plan/level/{id}', [PlanLevelController::class, 'destroy'])->name('delete_plan_levels');


// Under Time

Route::get('/under-time', [UnderTimeController::class, 'index'])->name('show_all_under_time_per_lines');
Route::post('/under-time', [UnderTimeController::class, 'store'])->name('create_under_time_per_lines');
Route::get('/under-time/{id}', [UnderTimeController::class, 'show'])->name('show_single_under_time_per_lines');
Route::put('/under-time/{id}', [UnderTimeController::class, 'update'])->name('edit_under_time_per_lines');
Route::delete('/under-time/{id}', [UnderTimeController::class, 'destroy'])->name('delete_under_time_per_lines');
