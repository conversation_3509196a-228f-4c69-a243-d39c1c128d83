<?php


namespace App\Traits\Users;

use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\LineDivisionUser;
use App\Position;
use App\PositionManager;
use App\Setting;
use App\User;
use App\UserPosition;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

trait IndexPerUser
{
    public function indexPerUser(User $user)
    {
        if ($user->hasRole("admin") || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $users = collect([]);
            return $users;
            // return User::whereNot('status','inactive')->pluck('id')->unique();
        }

        if ($user->hasPosition()) {
            $count = $user->lines()->count();
            $allLines = Line::select('id')->count();
            $allUsersPositions = collect([]);
            if ($count == $allLines) {
                $allUsersPositions = User::select('id')->pluck('id');
            }
            $positionUsers = $user->positionDivisions($user)->pluck('id')->push(auth()->id());
            $users = $positionUsers->merge($allUsersPositions);
            $positionsWithSameRole = collect();
            $divisionUsers = collect([]);

            $isAuthorizationPositionSetting = Setting::where('key', 'authorize_position_show_data')->value('value') === 'Yes';
            if ($isAuthorizationPositionSetting) {
                $positionsWithSameRole = $user->position()->users()->pluck('users.id');
                $positionDivisions = $user->userPosition->first()->divisions()->pluck('line_div_id');
                $divisionUsers = LineDivisionUser::whereIntegerInRaw('line_division_id', $positionDivisions)
                    ->where('from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('to_date', '>=', Carbon::now())
                        ->orWhere('to_date', null))->pluck('user_id');
                // throw new CrmException($positionDivisions);
            }
            $users = $users->merge($positionsWithSameRole);
            $users = $users->merge($divisionUsers);

            return $users->unique();
        }

        $users = $user->allBelowUsers()->pluck('id');
        $users->push($user->id);


        return $users->unique();
    }
}
