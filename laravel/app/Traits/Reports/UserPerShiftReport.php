<?php


namespace App\Traits\Reports;


use App\ActualVisit;
use App\DivisionType;
use App\Line;
use App\LineDivisionUser;
use App\OwActualVisit;
use App\OwPlanVisit;
use App\PlanVisit;
use App\User;
use App\UserPosition;
use App\Vacation;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

trait UserPerShiftReport
{

    private function getUsersWithoutVisits(array $allBelowUsersIds, string $from, string $to): array
    {
        $visitUsers = ActualVisit::select('user_id')
            ->whereIn('user_id', $allBelowUsersIds)
            ->whereBetween('visit_date', [$from, $to])
            ->where(DB::raw("TIME(visit_date)"), '<', self::above)
            ->where(DB::raw("TIME(visit_date)"), '>', self::below)
            ->pluck('user_id')->toArray();

        return array_diff($allBelowUsersIds, $visitUsers);
    }

    private function getUsersWithoutPlans(array $allBelowUsersIds, string $from, string $to): array
    {
        $plansUsers = PlanVisit::select('user_id')
            ->whereIn('user_id', $allBelowUsersIds)
            ->whereBetween('visit_date', [$from, $to])
            ->pluck('user_id')->toArray();

        return array_diff($allBelowUsersIds, $plansUsers);
    }

    private function getOfficeWorkPlansUsers($from, $to): array
    {
        return OwPlanVisit::select('user_id')
            ->whereBetween('day', [$from, $to])
            ->pluck('user_id')
            ->toArray();
    }

    private function getUsersOnVacation(string $from, string $to, ?int $shiftId = null): array
    {
        $query = Vacation::select('user_id')
            ->whereBetween("from_date", [$from, $to])
            ->orWhereBetween("to_date", [$from, $to])
            ->orWhere(function ($query) use ($from, $to) {
                $query
                    ->where("from_date", "<=", $from)
                    ->where("to_date", ">=", $to);
            })
            ->whereHas('details', fn($q) => $q->where('approval', 1))

            ->where(fn($query) => $query->where('shift_id', self::shiftId)->orWhereNull('shift_id'));


        return $query->get()->pluck('user_id')->toArray();
    }

    private function getOfficeWorkUsers(string $from, string $to): array
    {
        return OwActualVisit::select('user_id')
            ->whereBetween('date', [$from, $to])
            ->where(fn($q) => $q->where('shift_id', self::shiftId)->orWhereNull('shift_id'))
            ->pluck('user_id')
            ->toArray();
    }

    private function getAllBelowUsersIds(Collection $lines, User $currentUser, $from, $to): array
    {
        return $currentUser->belowUsersOfAllLinesWithPositions(
            $lines,
            from: $from,
            to: $to
        )->pluck('id')->toArray();
    }

    private function getLines(User $user, string $from, string $to): Collection
    {
        return $user->hasRole('admin') ?
            Line::where('from_date', '<=', $from)
                ->where(
                    fn($q) => $q->where('to_date', '>=', $to)
                        ->orWhere('to_date', null)
                )->get()
            : $user->lines()
                ->where('from_date', '<=', $from)
                ->where(
                    fn($q) => $q->where('to_date', '>=', $to)
                        ->orWhere('to_date', null)
                )->get();
    }

    private function getUsersOnPositions(): array
    {
        return UserPosition::select('user_id')->get()->pluck('user_id')->toArray();
    }

    private function getUsersOnHighLevel(): array
    {
        return LineDivisionUser::select(DB::raw("distinct user_id"))
            ->where("from_date", "<=", now())
            ->where(fn($q) => $q->where("to_date", ">=", now())->orWhere("to_date", null))
            ->whereHas(
                "linedivision.DivisionType",
                fn($q) => $q->where(
                    "level",
                    "<",
                    DivisionType::select(DB::raw("MAX(level) - 1"))
                )
            )
            ->get()
            ->pluck("user_id")
            ->toArray();

    }
}
