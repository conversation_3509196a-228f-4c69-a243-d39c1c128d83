<?php

namespace App\Models\PV;

use App\Doctor;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctorInformation extends Model
{
    use HasFactory;

    protected $guard_name = 'api';

    protected $table = 'doctor_information';

    protected $fillable = [
        'pv_id',
        'doctor_id',
        'qualification_id',
        'comment',
        'other_info',
        'email',
        'phone'
    ];
    public function doctor()
    {
        return $this->belongsTo(Doctor::class, 'doctor_id');
    }
    public function pv()
    {
        return $this->belongsTo(PV::class, 'pv_id');
    }
}
