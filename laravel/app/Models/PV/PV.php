<?php

namespace App\Models\PV;

use App\ActualVisit;
use App\PlanVisitDetails;
use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class PV extends Model
{
    use HasFactory;
    use HasRelationships;
    protected $guard_name = 'api';

    protected $table = 'p_v_s';

    protected $fillable = ['user_id', 'visit_id', 'pv_module_id', 'submition_date','additional_info','created_at','updated_at'];


    public function details()
    {
        return $this->morphOne(PlanVisitDetails::class, 'visitable');
    }
    public function approvalFlows()
    {
        return $this->hasManyDeepFromRelations(
            $this->details(),
            (new PlanVisitDetails)->approvalFlows()
        );
    }
    public function actual()
    {
        return $this->belongsTo(ActualVisit::class, 'visit_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function pvModule()
    {
        return $this->belongsTo(PvModule::class, 'pv_module_id');
    }
    public function drugs()
    {
        return $this->hasMany(DrugInformation::class, 'pv_id');
    }
    public function patient()
    {
        return $this->hasOne(PatientInformation::class, 'pv_id');
    }
    public function event()
    {
        return $this->hasOne(EventInformation::class, 'pv_id');
    }
    public function doctorInfo()
    {
        return $this->hasOne(DoctorInformation::class, 'pv_id');
    }

}
