<?php

namespace App\Models;

use App\AccountLines;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AccountLocationVerification extends Model
{
    use HasFactory;
    protected $table = 'account_location_verifications';
    protected $fillable = [
        'account_lines_id',
        'user_id',
        'verified'
    ];
    public function accountLines()
    {
        return $this->belongsTo(AccountLines::class, 'account_lines_id');
    }
}
