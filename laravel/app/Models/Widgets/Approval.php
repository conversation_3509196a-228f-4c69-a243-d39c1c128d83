<?php

namespace App\Models\Widgets;

use App\ActualVisit;
use App\ActualVisitSetting;
use App\DashboardSetting;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Interfaces\Widgets\FetchableInterface;
use App\Line;
use App\Models\ApprovalSetting;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\Expenses\Expense;
use App\Models\StartExpenseMonth;
use App\OwPlanVisit;
use App\PlanSetting;
use App\PlanVisit;
use App\Position;
use App\Scopes\Widgets\ApprovalScope;
use App\StartPlanDay;
use App\Traits\Widgets\BootableWidget;
use App\Traits\Widgets\Fetchable;
use App\Traits\Widgets\SettingsAccessAndAuthorizationAccess;
use App\User;
use App\UserActualStartDay;
use App\Vacation;
use App\VacationSetting;
use App\VisitProduct;
use App\Widget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Approval extends Widget implements FetchableInterface
{
    use SettingsAccessAndAuthorizationAccess, Fetchable, BootableWidget;
    public function fetchWidgetData(Widget $widget): Collection
    {

        /**@var User $user */
        $user = Auth::user();
        $approvalData = $user->userApprovals();
        $lines = $approvalData['lines'];
        $linesAapprovables = $approvalData['linesAapprovables'];
        $data = $this->getApprovals($lines, $linesAapprovables);

        return Collect(data_fill($widget, 'data', collect($data) ?? []));
    }
    private function vacations(User $user, $lines, $linesAapprovables)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $now = Carbon::now()->format('Y-m');
        $approvalSetting = ApprovalSetting::where('key', 'vacation_approval_center_flow')->value('value');
        $vactionSetting = VacationSetting::where('key', 'vacation_approval')->value('value');
        $vacationTime = StartExpenseMonth::where('name', $vactionSetting)->value('day');
        $scanLevel = 1;
        $vacations = Vacation::where('user_id', $user->id)
            // ->whereDate('from_date', '>=', Carbon::now()->toDateString())
            ->whereHas('details', function ($q) {
                $q->whereNull('approval');
            })->get()
            ->filter(function ($vacation) use ($approvalSetting, $authUser, $scanLevel, $lines, $linesAapprovables, $now, $vacationTime) {
                if ($approvalSetting == 'No') {
                    $vacationToDate = Carbon::parse($vacation->to_date)->addMonth($vacationTime)->format('Y-m');
                    if ($vacationToDate < $now) return;
                    return is_null($vacation->details?->approval);
                } else {
                    if ($vacation->details()->exists()) {
                        $approvablesCountOnThisShit = $vacation->details->approvalFlows()->count();
                        $data = $authUser->approvalWidget($vacation, $authUser, Vacation::class, null, null, $lines, $linesAapprovables);
                        $dataFlow = $data['linesDataFlow'];
                        $currentFlow = $dataFlow?->flow;
                        $required = $dataFlow?->required;
                        $vacantCount = $data['vacantCount'];
                        $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                        return $haveToApprove || $required;
                    }
                }
            })->count();

        return $vacations;
    }
    private function commercials(User $user, $linesAapprovables)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $authUserLines = $authUser->lines()->pluck('lines.id')->toArray();
        $approvalSetting = ApprovalSetting::where('key', 'commercial_approval_center_flow')->value('value');
        $scanLevel = 1;

        $commercials = CommercialRequest::where('user_id', $user->id)->where('archived', 0)
            // ->whereHas('details', fn($q) => $q->whereNull('approval'))
            ->whereDoesntHave('details', function ($query) use ($authUser) {
                $query->where('user_id', $authUser->id);
            })
            ->withCount("approvalFlows")
            ->with(["details", "getCommercialLines", "approvalFlows" => function ($q) {
                $q->where('approval_flow_users.approval', 0);
            }])->get()
            ->filter(function ($commercial) use ($approvalSetting, $authUser, $scanLevel, $linesAapprovables, $authUserLines) {
                if ($approvalSetting == 'No') {
                    return is_null($commercial->details?->approval);
                } else {
                    if ($commercial->details()->exists()) {
                        $lines = $commercial->getCommercialLines;
                        $disapproved = $commercial->approvalFlows->first();
                        if ($disapproved) return;
                        $approvablesCountOnThisShit = $commercial->approval_flows_count;
                        $data = $authUser->approvalWidget(
                            $commercial,
                            $authUser,
                            CommercialRequest::class,
                            Carbon::parse($commercial->created_at)->firstOfMonth()->toDateString(),
                            Carbon::parse($commercial->created_at)->endOfMonth()->toDateString(),
                            $lines,
                            $linesAapprovables,
                            $authUserLines
                        );
                        $dataFlow = $data['linesDataFlow'];
                        $currentFlow = $dataFlow?->flow;
                        $vacantCount = $data['vacantCount'];
                        $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                        return $haveToApprove;
                    }
                }
            })->count();

        return $commercials;
    }
    private function expenses(User $user, $linesAapprovables)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $approvalSetting = ApprovalSetting::where('key', 'expense_approval_center_flow')->value('value');
        $scanLevel = 1;
        $expenses = Expense::where('user_id', $user->id)->whereHas('approvals', fn($q) => $q->whereNull('approval'))
            ->with(['details' => function ($q) {
                $q = $q->whereDate('date', '>=', Carbon::now()->toDateString());
            }, 'line'])->get()->filter(function ($expense) use ($approvalSetting, $authUser, $scanLevel, $linesAapprovables) {
                if ($approvalSetting == 'No') {
                    return is_null($expense->approvals?->approval);
                } else {
                    if (!isNullable($expense->approvals)) {
                        $lines = $expense->line()->get();
                        $from = Carbon::parse($expense->date)->startOfMonth();
                        $to = Carbon::parse($expense->date)->endOfMonth();
                        $flows = $expense->approvals;
                        $disapproved = $flows->approvalFlows()->where('approval', 0)->first();
                        if ($disapproved) return;
                        $approvablesCountOnThisShit = $flows->approvalFlows()->count();
                        // $approvablesCountOnThisShit = $expense->approvals?->approvalFlows()->count();
                        $data = $authUser->approvalWidget($expense, $authUser, Expense::class, $from, $to, $lines, $linesAapprovables);
                        $dataFlow = $data['linesDataFlow'];
                        $currentFlow = $dataFlow?->flow;
                        $vacantCount = $data['vacantCount'];
                        $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                        return $haveToApprove;
                    }
                }
            })->count();

        return $expenses;
    }
    private function visits(User $user)
    {
        return PlanVisit::where('user_id', $user->id)
            ->where('visit_date', '>=', Carbon::now()->toDateString())
            ->whereHas('details', fn($q) => $q->whereNull('approval')->whereNull('user_id'))->count();
    }
    private function actuals(User $user)
    {
        $countActuals = ActualVisit::where('user_id', $user->id)
            ->where('visit_date', '>=', Carbon::now()->toDateString())->get()
            ->filter(fn($actual)
            => is_null($actual->details?->approval))
            ->count();


        return $countActuals;
    }
    private function ow(User $user)
    {
        $countPlans = OwPlanVisit::where('user_id', $user->id)
            ->where('day', '>=', Carbon::now()->toDateString())
            ->whereHas('details', fn($q) => $q->whereNull('approval')->whereNull('user_id'))->count();


        return $countPlans;
    }

    public function getPlanApprovals($lines)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        foreach ($lines as $line) {
            $users = $users->merge($user->planableUsers($line->id, PlanVisit::class, DivisionType::class));
            $users = $users->merge($user->planableUsers($line->id, PlanVisit::class, Position::class));
        }
        $users =  $users->map(function ($user) {
            return [
                'id' => $user->id,
                'emp_code' => $user->emp_code ?? '',
                'full_name' => $user->fullname,
                'position' => $user->menuroles,
                'number_of_visits' => $this->visits($user),
            ];
        })->unique("id")->values();

        $setting = DashboardSetting::where('key', 'appear_all_approval_employees')->value('value');
        if ($setting == 'No') $users = $users->filter(fn($user) => $user['number_of_visits'] != 0);

        return $users->unique("id")->values();
    }
    public function getActualApprovals($lines, $linesAapprovables)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        foreach ($lines as $line) {
            $users = $users->merge($user->planableUsers($line->id, ActualVisit::class, DivisionType::class));
            $users = $users->merge($user->planableUsers($line->id, ActualVisit::class, Position::class));
        }
        $users =  $users->map(function ($user) {
            return [
                'id' => $user->id,
                'emp_code' => $user->emp_code ?? '',
                'full_name' => $user->fullname,
                'position' => $user->menuroles,
                'number_of_actuals' => $this->actuals($user),
            ];
        })->unique("id")->values();

        $setting = DashboardSetting::where('key', 'appear_all_approval_employees')->value('value');
        if ($setting == 'No') $users = $users->filter(fn($user) => $user['number_of_actuals'] != 0);

        return $users->unique("id")->values();
    }
    public function getOwApprovals($lines, $linesAapprovables)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        foreach ($lines as $line) {
            $users = $users->merge($user->planableUsers($line->id, PlanVisit::class, DivisionType::class));
            $users = $users->merge($user->planableUsers($line->id, PlanVisit::class, Position::class));
        }
        $users =  $users->map(function ($user) {
            return [
                'id' => $user->id,
                'emp_code' => $user->emp_code ?? '',
                'full_name' => $user->fullname,
                'position' => $user->menuroles,
                'number_of_ow' => $this->ow($user),
            ];
        })->unique("id")->values();
        $setting = DashboardSetting::where('key', 'appear_all_approval_employees')->value('value');
        if ($setting == 'No') $users = $users->filter(fn($user) => $user['number_of_ow'] != 0);
        return $users->unique("id")->values();
    }
    public function getVacationApprovals($lines, $linesAapprovables)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        foreach ($lines as $line) {

            $users = $users->merge($user->planableUsers($line->id, Vacation::class, DivisionType::class));
            $users = $users->merge($user->planableUsers($line->id, Vacation::class, Position::class));
        }
        $users =  $users->map(function ($user) use ($lines, $linesAapprovables) {
            return [
                'id' => $user->id,
                'emp_code' => $user->emp_code ?? '',
                'full_name' => $user->fullname,
                'position' => $user->menuroles,
                'number_of_vacations' => $this->vacations($user, $lines, $linesAapprovables),
            ];
        })->unique("id")->values();
        $setting = DashboardSetting::where('key', 'appear_all_approval_employees')->value('value');
        if ($setting == 'No') $users = $users->filter(fn($user) => $user['number_of_vacations'] != 0);
        return $users->unique("id")->values();
    }
    public function getCommercialApprovals($lines, $linesAapprovables)
    {
        /**@var User $user */
        $user = Auth::user();

        $users = collect([]);
        foreach ($lines as $line) {
            $users = $users->merge($user->planableUsers($line->id, CommercialRequest::class, DivisionType::class));
        }
        $users =  $users->map(function ($user) use ($lines, $linesAapprovables) {
            return [
                'id' => $user->id,
                'emp_code' => $user->emp_code ?? '',
                'full_name' => $user->fullname,
                'position' => $user->menuroles,
                'number_of_commercials' => $this->commercials($user, $linesAapprovables),
            ];
        })->unique("id")->values();
        $setting = DashboardSetting::where('key', 'appear_all_approval_employees')->value('value');
        if ($setting == 'No') $users = $users->filter(fn($user) => $user['number_of_commercials'] != 0);
        return $users->unique("id")->values();
    }
    public function getExpenseApprovals($lines, $linesAapprovables)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        foreach ($lines as $line) {
            $users = $users->merge($user->planableUsers($line->id, Expense::class, DivisionType::class));
        }
        $users =  $users->map(function ($user) use ($lines, $linesAapprovables) {
            return [
                'id' => $user->id,
                'emp_code' => $user->emp_code ?? '',
                'full_name' => $user->fullname,
                'position' => $user->menuroles,
                'number_of_expenses' => $this->expenses($user, $linesAapprovables),
            ];
        })->unique("id")->values();
        $setting = DashboardSetting::where('key', 'appear_all_approval_employees')->value('value');
        if ($setting == 'No') $users = $users->filter(fn($user) => $user['number_of_expenses'] != 0);
        return $users->unique("id")->values();
    }
    public function getApprovals($lines, $linesAapprovables)
    {
        /**@var User */
        $user = Auth::user();
        $planApprovals = collect([]);
        $owApprovals = collect([]);
        $vacationApprovals = collect([]);
        // if ($user->hasPermissionTo('create_approve_plan') && $user->hasPermissionTo('create_approve_plan')) {
        $planApprovals = $this->getPlanApprovals($lines, $linesAapprovables);
        $owApprovals = $this->getOwApprovals($lines, $linesAapprovables);
        // }
        // if ($user->hasPermissionTo('create_approve_actual') && $user->hasPermissionTo('create_disapprove_actual'))
        // $actualApprovals = $this->getActualApprovals($lines, $linesAapprovables);
        // if ($user->hasPermissionTo('create_approve_vacation') && $user->hasPermissionTo('create_disapprove_vacation'))
        $vacationApprovals = $this->getVacationApprovals($lines, $linesAapprovables);
        $commercialApprovals = $this->getCommercialApprovals($lines, $linesAapprovables);
        $expenseApprovals = $this->getExpenseApprovals($lines, $linesAapprovables);
        $approvals = [];
        // if (count($planApprovals) > 0) {
        //     $approvals = array_merge(['Plans' => $planApprovals->toArray()]);
        // }
        $approvals[] = array_merge($approvals, [
            'Plans' => $planApprovals->toArray(),
            // 'Actuals' => $actualApprovals->toArray(),
            'Ow' => $owApprovals->toArray(),
            'Vacations' => $vacationApprovals->toArray(),
            'Commercials' => $commercialApprovals->toArray(),
            'Expenses' => $expenseApprovals->toArray(),
        ]);
        return $approvals;
    }
}
