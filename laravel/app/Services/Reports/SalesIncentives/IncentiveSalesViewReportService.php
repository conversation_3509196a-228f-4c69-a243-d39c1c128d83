<?php

namespace App\Services\Reports\SalesIncentives;

use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\Product;
use App\Role;
use App\Services\Enums\KPITypes;
use App\Services\Reports\SalesIncentives\Traits\BaseSalesTargets;
use App\Services\Reports\SalesIncentives\Traits\CacheUtils;
use App\Services\Reports\SalesIncentives\Traits\IncentiveCalculations;
use App\Services\Reports\SalesIncentives\Traits\ServiceConfigurable;
use App\Services\Sales\SalesIncentiveHolder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class IncentiveSalesViewReportService
{

    use CacheUtils;
    use BaseSalesTargets;
    use ServiceConfigurable;


    const TOTAL_PRODUCT_VALUE_PERCENTAGE = 0.8;
    const MR_PRODUCT_VALUE_PERCENTAGE = 0.2;
    const MR_KPIS_PERCENTAGE = 0.05;
    const ONE_HUNDRED_PERCENTAGE = 100;
    const SEVENTY_FIVE_RATIO = 0.75;
    const TWENTY_FIVE_RATIO = 0.25;
    const ZERO_DEFAULT = 0.0;
    const TWO_DECIMAL_ROUND = 2;

    public function __construct(private readonly SalesIncentiveHolder $salesIncentiveHolder)
    {
    }

    private int $cacheTimeout = 60;
    private int $perDivOrUserFilter;
    private Carbon $from;
    private Carbon $to;
    private bool $isBrickChecked;
    private array $years = [];
    private array $months = [];
    private int $divisionType;
    private mixed $mappingType;


    public function setCacheTimeout(int $cacheTimeout): self
    {
        $this->cacheTimeout = $cacheTimeout;
        return $this;
    }

    public function setPerDivOrUserFilter(int $perDivOrUserFilter): self
    {
        $this->perDivOrUserFilter = $perDivOrUserFilter;
        return $this;
    }

    public function setFrom(Carbon $from): self
    {
        $this->from = $from;
        return $this;
    }

    public function setTo(Carbon $to): self
    {
        $this->to = $to;
        return $this;
    }

    public function setIsChecked(bool $isBrickChecked): self
    {
        $this->isBrickChecked = $isBrickChecked;
        return $this;
    }

    public function setYears(array $years): self
    {
        $this->years = $years;
        return $this;
    }

    public function setMonths(array $months): self
    {
        $this->months = $months;
        return $this;
    }

    public function setDivisionType(int $divisionType): self
    {
        $this->divisionType = $divisionType;
        return $this;
    }

    public function setMappingType(mixed $mappingType): self
    {
        $this->mappingType = $mappingType;
        return $this;
    }


    public function getProductDataPerProduct(Collection $collection, $product): Collection
    {
        $productExists = $collection->has($product->id);
        return $productExists ? $collection->get($product->id) : collect();
    }

    public function getProductDataPerDate(Collection $collection, $date)
    {
        return $collection->where("date", $date)->first();
    }

    public function getProductTargetsSales(Collection $sales, Collection $targets, Product $product, $date = null): array
    {
        $salesCollection = $this->getProductDataPerProduct($sales, $product);
        $targetsCollection = $this->getProductDataPerProduct($targets, $product);

        if (is_null($date)) {
            $salesUnit = $salesCollection->sum("quantity");
            //            $salesValue = $salesCollection->sum("value");
            $salesValue = $salesCollection->sum("sales_value");

            $targetsUnit = $targetsCollection->sum("target");
            $targetsValue = $targetsCollection->sum("value");
            $targetsValue = $targetsValue != 0 ? $targetsValue : $targetsCollection->sum("target_value");


            return [
                ['salesUnit' => round($salesUnit), 'salesValue' => round($salesValue, self::TWO_DECIMAL_ROUND)],
                ['targetUnit' => round($targetsUnit), 'targetValue' => round($targetsValue, self::TWO_DECIMAL_ROUND)]
            ];
        }

        $date = $date->format('Y-m');
        $sale = $this->getProductDataPerDate($salesCollection, $date);


        $salesUnit = $sale ? $sale->quantity : self::ZERO_DEFAULT;

        if ($sale) {
            $salesValue = $sale->value != 0 ? $sale->value : $sale->sales_value;
        } else {
            $salesValue = self::ZERO_DEFAULT;
        }

        $target = $this->getProductDataPerDate($targetsCollection, $date);
        $targetsUnit = $target ? $target->target : self::ZERO_DEFAULT;
        if ($target) {
            $targetsValue = $target->value != 0 ? $target->value : $target->target_value;
        } else {
            $targetsValue = self::ZERO_DEFAULT;
        }
        // $targetsValue = $target ? $target->target_value : 0;

        return [
            ['salesUnit' => round($salesUnit), 'salesValue' => round($salesValue, self::TWO_DECIMAL_ROUND)],
            ['targetUnit' => round($targetsUnit), 'targetValue' => round($targetsValue, self::TWO_DECIMAL_ROUND)]
        ];
    }


    // district
    public function salesView(LineDivision $object, Line $line, Collection $products, bool $groupByBrand = true): Collection
    {
        $belowDivisions = $this->getBelowDivisions($object, $line);
        $result = $this->getProductDataProcessingInSalesView($products, $line, $object, $belowDivisions);

        return $groupByBrand ? $this->groupProductsByBrand($result) :  $result;
        // return $result;
    }


    private function getBelowDivisions($object, Line $line)
    {
        $cacheKey = "below_divisions_{$this->perDivOrUserFilter}_{$object->id}_{$line->id}_{$this->divisionType}";
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($object, $line) {
            return $object->getBelowDivisions($this->from, $this->to)->where('division_type_id', '=', $this->divisionType)->unique('id')->pluck('id')->toArray();
        });
    }


    private function getProductDataProcessingInSalesView(Collection $lineProducts, Line $line, LineDivision $object, $belowDivisions): Collection
    {
        $sumSalesUnits = self::ZERO_DEFAULT;
        $sumSalesValues = self::ZERO_DEFAULT;
        $sumTargetUnits = self::ZERO_DEFAULT;
        $sumTargetValues = self::ZERO_DEFAULT;
        $sumAchievementUnits = self::ZERO_DEFAULT;
        $sumAchievementValues = self::ZERO_DEFAULT;
        $total_product_values = self::ZERO_DEFAULT;

        $sales = $this->sales(
            $this->from->format('Y-m-d'),
            $this->to->format('Y-m-d'),
            $belowDivisions,
            $lineProducts->pluck("product_id")->toArray(),
            $line->id,
            mappingTypeId: $this->mappingType,
        );
        $targets = $this->targets(
            $belowDivisions,
            $lineProducts->pluck("product_id")->toArray(),
            $this->months,
            $this->years
        );

        /**
         * @var Collection
         */
        $employees = $object->users($this->from, $this->to)->where('line_users_divisions.line_id', $line->id)->get();
        $color = $object?->DivisionType->color;
        $user = $employees->first();
        $role = $object?->DivisionType->role;
        $employee_name = $employees->pluck('fullname')->implode(',');
        $emp_code = $employees->pluck('emp_code')->implode(',');
        $division_name = $object?->name;
        $inCharge_date = $user?->pivot?->from_date ?? '';
        $excused_date = $user?->pivot?->to_date ?? '';

        $charge_dates = $inCharge_date . ' ' . $excused_date;

        $coverage = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::COVERAGE, $role?->id, $user?->id);
        $frequency = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::FREQUENCY, $role?->id, $user?->id);
        $callRate = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::CALL_RATE, $role?->id, $user?->id);
        $coachingRatio = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::COACHING_RATIO, $role?->id, $user?->id);
        $coveredCoaching = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::COVERED_COACHING, $role?->id, $user?->id);
        $vacantRatio = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::VACANT_RATIO, $role?->id, $user?->id);
        $managerCoverage = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::MANAGER_COVERAGE, $role?->id, $user?->id);
        $mkRatio = $this->salesIncentiveHolder->calculateKpiValue(KPITypes::M_K, $role?->id, $user?->id);


        $firstIncentive = $this->salesIncentiveHolder->getFirstIncentive($role?->id);

        $userRelatedFields = compact(
            'employee_name',
            'emp_code',
            'division_name',
            'role',
            'color'
        );


        $data = $lineProducts->map(function ($productData)
        use (
            $userRelatedFields,
            $line,
            $object,
            $belowDivisions,
            $employees,
            $role,
            $color,
            $sales,
            $targets,
            $firstIncentive,
            &$sumSalesUnits,
            &$sumSalesValues,
            &$sumTargetUnits,
            &$sumTargetValues,
            &$sumAchievementUnits,
            &$sumAchievementValues,
            &$total_product_values
        ) {

            $product = $productData->product;

            [$sale, $target] = $this->getProductTargetsSales($sales, $targets, $product);

            $achievementUnits = $target['targetUnit']
                ? (round($sale['salesUnit'] / $target['targetUnit'] * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND))
                : self::ZERO_DEFAULT;
            $achievementValues = $target['targetValue']
                ? (round($sale['salesValue'] / $target['targetValue'] * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND))
                : self::ZERO_DEFAULT;
            $sumSalesUnits += $sale['salesUnit'];
            $sumSalesValues += $sale['salesValue'];
            $sumTargetUnits += $target['targetUnit'];
            $sumTargetValues += $target['targetValue'];
            $sumAchievementUnits += $achievementUnits;
            $sumAchievementValues += $achievementValues;

            $product_value = $this->salesIncentiveHolder->calculateProductValue(
                $role,
                $achievementValues,
                $firstIncentive,
                $productData->p_w
            );
            $total_product_values += $product_value;
            return [
                'id' => $object->id,
                'role_id' => $role?->id,
                'line' => $line->name,
                'division' => $userRelatedFields['division_name'],
                'employee' => $userRelatedFields['employee_name'],
                'emp_code' => $userRelatedFields['emp_code'],
                'product' => $product->name,
                'p_w' => round($productData->p_w, self::TWO_DECIMAL_ROUND),
                'product_value' => $product_value,
                'brand' => $product->brands()?->first()?->name ?? '',
                'sales_unit' => $sale['salesUnit'],
                'sales_value' => round($sale['salesValue'], self::TWO_DECIMAL_ROUND),
                'target_unit' => $target['targetUnit'],
                'target_value' => round($target['targetValue'], self::TWO_DECIMAL_ROUND),
                'achievement_unit' => $achievementUnits . '%',
                'achievement_value' => round($achievementValues, self::TWO_DECIMAL_ROUND) . '%',
                'color' => $color,
                'level' => $object->DivisionType->level,
                'is_total' => false,
                'is_brand_summary' => false, // Individual product record
            ];
        });

        $achievement_value = $sumTargetValues ? $sumSalesValues / $sumTargetValues * self::ONE_HUNDRED_PERCENTAGE : self::ZERO_DEFAULT;
        $VALUE = $firstIncentive?->achievement_rule ?? INF;

        [$coverage, $frequency, $callRate, $total_product_values] = $achievement_value > $VALUE
            ? [$coverage, $frequency, $callRate, $total_product_values] : array_fill(0, 4, self::ZERO_DEFAULT);

        $kpis = $coverage + $frequency + $callRate;

        $eighty_total_percent_product_value = $total_product_values * self::TOTAL_PRODUCT_VALUE_PERCENTAGE;

        $parentEmployees = LineDivision::find($object?->parent_id)
            ?->users($this->from, $this->to)
            ?->with("roles")
            ?->where('line_users_divisions.line_id', $line->id)
            ?->get() ?? collect();

        $parentRole = $parentEmployees->first()?->roles?->first();

        $this->salesIncentiveHolder->trackManagerIncentive(
            $object->parent_id,
            $parentRole?->id,
            $eighty_total_percent_product_value
        );

        $incentiveValue = $this->salesIncentiveHolder->getIncentivePerAchievement($achievement_value, $role?->id);


        $kpi_value = (($kpis * self::MR_KPIS_PERCENTAGE) * (self::MR_PRODUCT_VALUE_PERCENTAGE * $incentiveValue)); // MR

        $total_incentive = $eighty_total_percent_product_value + $kpi_value;

        $districtKPIS = $coachingRatio + $coveredCoaching + $vacantRatio;

        $total = [
            'id' => $object->id,
            'line' => $line->name,
            'in_charge_date' => $charge_dates,
            'division' => $userRelatedFields['division_name'],
            'employee' => $userRelatedFields['employee_name'],
            'emp_code' => $userRelatedFields['emp_code'],
            'product' => 'Total',
            'p_w' => '',
            'product_value' => round($eighty_total_percent_product_value, self::TWO_DECIMAL_ROUND),
            "cov" => $coverage,
            "freq" => $frequency,
            "call_r" => $callRate,
            "kpis_value" => round($kpi_value, self::TWO_DECIMAL_ROUND),
            "coaching_ratio" => $coachingRatio,
            "covered_coaching" => $coveredCoaching,
            "vacant_ratio" => $vacantRatio,
            "manager_coverage" => $managerCoverage,
            "m_k_ratio" => $mkRatio,
            "total_incentive" => round($total_incentive, self::TWO_DECIMAL_ROUND),
            '75%' => round($total_incentive * self::SEVENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND),
            '25%' => round($total_incentive * self::TWENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND),
            'brand' => '',
            'sales_unit' => $sumSalesUnits,
            'sales_value' => $sumSalesValues
                ? round($sumSalesValues, self::TWO_DECIMAL_ROUND)
                : self::ZERO_DEFAULT,
            'target_unit' => $sumTargetUnits,
            'target_value' => $sumTargetValues
                ? round($sumTargetValues, self::TWO_DECIMAL_ROUND)
                : self::ZERO_DEFAULT,
            'achievement_unit' => $sumTargetUnits
                ? (round($sumSalesUnits / $sumTargetUnits * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND)) . '%'
                : self::ZERO_DEFAULT . '%',
            'achievement_value' => (round($achievement_value, self::TWO_DECIMAL_ROUND)) . '%',
            'color' => $color,
            'is_total' => true,
            'is_brand_summary' => false, // Total record is not a brand summary
            'level' => $object->DivisionType->level,
            'kpis' => $kpis,
            'district_kpis' => $districtKPIS,
            "parent_id" => $object->parent_id,
            "parent_role_id" => $parentRole?->id,
            'position_ids' => $object->positions($this->from, $this->to)->get()->pluck('id')->toArray(),
        ];


        return $data->add($total);
    }

    /**
     * Group products by brand with brand summary records
     *
     * This method creates a hierarchical structure where each brand gets a summary record
     * followed by its individual products. The structure will be:
     * [Brand Summary] -> [Product 1] -> [Product 2] -> [Next Brand Summary] -> [Product 3]...
     *
     * Example usage:
     * $service = new IncentiveSalesViewReportService($salesIncentiveHolder);
     * $result = $service->salesView($division, $line, $products, true); // true for groupByBrand
     *
     * Or manually:
     * $individualResults = $service->salesView($division, $line, $products);
     * $groupedResults = $service->groupProductsByBrand($individualResults);
     *
     * @param Collection $data Collection of individual product records
     * @return Collection Collection with brand summaries followed by individual products + total records
     */
    public function groupProductsByBrand(Collection $data): Collection
    {
        // Separate total records from product records
        $totals = $data->where('is_total', true);
        $products = $data->where('is_total', false);

        // Group products by brand and employee (treating empty brand as separate group)
        $groupedByBrandAndEmployee = $products->groupBy(function ($item) {
            $brandKey = $item['brand'] ?: 'No Brand';
            $employeeKey = $item['employee'] ?: 'Unknown';

            // When employee is unknown, include division for better grouping
            if ($employeeKey === 'Unknown') {
                $divisionKey = $item['division'] ?: 'Unknown Division';
                $employeeKey = 'Unknown (' . $divisionKey . ')';
            }

            return $brandKey . '|' . $employeeKey; // Combine brand and employee for unique grouping
        });

        $result = collect();

        $brandSummaries = collect();


        foreach ($groupedByBrandAndEmployee as $groupKey => $brandProducts) {
            [$brandName] = explode('|', $groupKey);
            $firstProduct = $brandProducts->first();

            // Log::info($firstProduct);

            $firstIncentive = $this->salesIncentiveHolder->getFirstIncentive($firstProduct['role_id']);

            $brandProductWeight = $brandProducts->sum('p_w');
            $achievementValues = (float)$this->calculateAchievementPercentage(
                $brandProducts->sum('sales_value'),
                $brandProducts->sum('target_value')
            );

            $product_value = $this->salesIncentiveHolder->calculateProductValue(
                Role::find($firstProduct['role_id']),
                $achievementValues,
                $firstIncentive,
                $brandProductWeight
            );

            // Create brand summary record
            $brandSummary = [
                'id' => $firstProduct['id'],
                'line' => $firstProduct['line'],
                'division' => $firstProduct['division'],
                'employee' => $firstProduct['employee'],
                'emp_code' => $firstProduct['emp_code'],
                'product' => $brandName, // Use brand name as product name
                'p_w' => round($brandProductWeight, self::TWO_DECIMAL_ROUND),
                'product_value' => round($product_value, self::TWO_DECIMAL_ROUND),
                'brand' => $brandName === 'No Brand' ? '' : $brandName,
                'sales_unit' => $brandProducts->sum('sales_unit'),
                'sales_value' => round($brandProducts->sum('sales_value'), self::TWO_DECIMAL_ROUND),
                'target_unit' => $brandProducts->sum('target_unit'),
                'target_value' => round($brandProducts->sum('target_value'), self::TWO_DECIMAL_ROUND),
                'achievement_unit' => $this->calculateAchievementPercentage(
                    $brandProducts->sum('sales_unit'),
                    $brandProducts->sum('target_unit')
                ),
                'achievement_value' => $achievementValues,
                'color' => $firstProduct['color'],
                'level' => $firstProduct['level'],
                'is_total' => false,
                'is_brand_summary' => true, // NEW: Flag to identify brand summary records
                'product_count' => $brandProducts->count(),
            ];

            if ($brandName !== 'No Brand') {
                // Add brand summary first
                $result->push($brandSummary);

                $brandSummaries->push($brandSummary);
            }


            if ($brandName === 'No Brand') {
                foreach ($brandProducts as $product) {
                    $product['is_brand_summary'] = false; // Ensure individual products are marked
                    $result->push($product);
                    $brandSummaries->push($product);
                }
            }


        }

        // Recalculate totals based on the actual product data (same logic as getProductDataProcessingInSalesView)
        $recalculatedTotals = $this->recalculateTotalsFromProducts($brandSummaries, $totals->first());


        return $result->concat(collect([$recalculatedTotals]));
    }

    /**
     * Recalculate totals based on actual product data using the same logic as getProductDataProcessingInSalesView
     *
     * @param Collection $products Collection of individual product records
     * @param array|null $originalTotal Original total record for reference
     * @return array Recalculated total record
     */
    private function recalculateTotalsFromProducts(Collection $products, ?array $originalTotal): array
    {
        if (!$originalTotal) {
            return [];
        }

        // Recalculate sums from actual product data (same as getProductDataProcessingInSalesView)
        $sumSalesUnits = $products->sum('sales_unit');
        $sumSalesValues = $products->sum('sales_value');
        $sumTargetUnits = $products->sum('target_unit');
        $sumTargetValues = $products->sum('target_value');

        // Recalculate total product values from individual products
        $total_product_values = $products->sum('product_value');

        // Use the same achievement calculation logic as getProductDataProcessingInSalesView
        $achievement_value = $sumTargetValues ? $sumSalesValues / $sumTargetValues * self::ONE_HUNDRED_PERCENTAGE : self::ZERO_DEFAULT;

        // Get first product to extract role information for incentive calculations
        $firstProduct = $products->first();
        $firstIncentive = $firstProduct ? $this->salesIncentiveHolder->getFirstIncentive($firstProduct['role_id']) : null;
        $VALUE = $firstIncentive?->achievement_rule ?? INF;

        // Apply the same conditional logic as getProductDataProcessingInSalesView
        $eighty_total_percent_product_value = $total_product_values * self::TOTAL_PRODUCT_VALUE_PERCENTAGE;

        // Preserve KPI values from original total (these don't change based on product grouping)
        $coverage = $originalTotal['cov'] ?? self::ZERO_DEFAULT;
        $frequency = $originalTotal['freq'] ?? self::ZERO_DEFAULT;
        $callRate = $originalTotal['call_r'] ?? self::ZERO_DEFAULT;

        // Apply achievement rule condition
        [$coverage, $frequency, $callRate, $total_product_values] = $achievement_value > $VALUE
            ? [$coverage, $frequency, $callRate, $total_product_values] : array_fill(0, 4, self::ZERO_DEFAULT);

        $kpis = $coverage + $frequency + $callRate;
        $eighty_total_percent_product_value = $total_product_values * self::TOTAL_PRODUCT_VALUE_PERCENTAGE;

        // Recalculate incentive values
        $role = $firstProduct ? Role::find($firstProduct['role_id']) : null;
        $incentiveValue = $this->salesIncentiveHolder->getIncentivePerAchievement($achievement_value, $role?->id);
        $kpi_value = (($kpis * self::MR_KPIS_PERCENTAGE) * (self::MR_PRODUCT_VALUE_PERCENTAGE * $incentiveValue));
        $total_incentive = $eighty_total_percent_product_value + $kpi_value;

        // Create updated total record with recalculated values
        return array_merge($originalTotal, [
            'product_value' => round($eighty_total_percent_product_value, self::TWO_DECIMAL_ROUND),
            'kpis_value' => round($kpi_value, self::TWO_DECIMAL_ROUND),
            'total_incentive' => round($total_incentive, self::TWO_DECIMAL_ROUND),
            '75%' => round($total_incentive * self::SEVENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND),
            '25%' => round($total_incentive * self::TWENTY_FIVE_RATIO, self::TWO_DECIMAL_ROUND),
            'sales_unit' => $sumSalesUnits,
            'sales_value' => $sumSalesValues ? round($sumSalesValues, self::TWO_DECIMAL_ROUND) : self::ZERO_DEFAULT,
            'target_unit' => $sumTargetUnits,
            'target_value' => $sumTargetValues ? round($sumTargetValues, self::TWO_DECIMAL_ROUND) : self::ZERO_DEFAULT,
            'achievement_unit' => $sumTargetUnits
                ? (round($sumSalesUnits / $sumTargetUnits * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND)) . '%'
                : self::ZERO_DEFAULT . '%',
            'achievement_value' => (round($achievement_value, self::TWO_DECIMAL_ROUND)) . '%',
            'kpis' => $kpis,
            'cov' => $coverage,
            'freq' => $frequency,
            'call_r' => $callRate,
        ]);
    }

    /**
     * Calculate achievement percentage for grouped data
     *
     * @param float $actual
     * @param float $target
     * @return string
     */
    private function calculateAchievementPercentage($actual, $target): string
    {
        if ($target == 0) {
            return self::ZERO_DEFAULT . '%';
        }
        return round($actual / $target * self::ONE_HUNDRED_PERCENTAGE, self::TWO_DECIMAL_ROUND) . '%';
    }
}
