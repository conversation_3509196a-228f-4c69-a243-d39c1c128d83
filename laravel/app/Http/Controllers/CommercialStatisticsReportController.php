<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\CategoryType;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\PaymentMethod;
use App\RequestType;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CommercialStatisticsReportController extends ApiController
{
    public function getData()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $requestTypes = RequestType::select('id', 'name')->whereNull('deleted_at')->orderBy('sort', 'ASC')->get();
        $categoriesTypes = CategoryType::select('id', 'name')->where('parent_id',0)->get();
        $payments = PaymentMethod::get();
        return $this->respond(compact('lines', 'requestTypes', 'payments','categoriesTypes'));
    }
    public function getCommercials($commercial, $object, $table, $from, $to, $type,$authUser)
    {
        $commercials = DB::table('commercial_requests')->select(
            'commercial_requests.id as id',
            'commercial_requests.from_date as from',
            'request_types.name as type',
            'commercial_requests.to_date as to',
            'users.name as employee',
            'users.id as user_id',
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            DB::raw('IFNULL(group_concat(distinct crm_commercial_products.ratio),"") as ratio'),
            DB::raw('IFNULL(group_concat(distinct crm_accounts.name),"") as pharmacy'),
            DB::raw('IFNULL(group_concat(distinct crm_division_types.color),"") as color'),
            DB::raw('IFNULL(group_concat(distinct crm_commercial_requests.description),"") as description'),
            DB::raw('IFNULL(group_concat(distinct crm_doctors.name),"") as doctors'),
            DB::raw('IFNULL(group_concat(distinct crm_employees.name),"") as employees'),
            DB::raw('IFNULL(crm_paid_requests.amount,"") as total'),
        )
            ->leftJoin('users', 'commercial_requests.user_id', 'users.id')
            ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->leftJoin('commercial_lines', 'commercial_requests.id', 'commercial_lines.request_id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', CommercialRequest::class);
                }
            )
            ->leftJoin(
                'paid_requests',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'paid_requests.paidable_id');
                    $join->where('paid_requests.paidable_type', CommercialRequest::class);
                }
            )
            ->leftJoin('lines', 'commercial_lines.line_id', 'lines.id')
            ->leftJoin('commercial_divisions', 'commercial_requests.id', 'commercial_divisions.request_id')
            ->leftJoin('line_divisions', 'commercial_divisions.div_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('commercial_products', 'commercial_requests.id', 'commercial_products.request_id')
            ->leftJoin('products', 'commercial_products.product_id', 'products.id')
            ->leftJoin('commercial_doctors', 'commercial_requests.id', 'commercial_doctors.request_id')
            ->leftJoin('commercial_user_cost_types', 'commercial_requests.id', 'commercial_user_cost_types.request_id')
            ->leftJoin('doctors', 'commercial_doctors.doctor_id', 'doctors.id')
            ->leftJoin('users as employees', 'commercial_user_cost_types.user_id', 'employees.id')
            ->leftJoin('commercial_pharmacies', 'commercial_requests.id', 'commercial_pharmacies.request_id')
            ->leftJoin('accounts', 'commercial_pharmacies.pharmacy_id', 'accounts.id')
            ->orderBy('id', 'DESC')
            ->whereNull('commercial_requests.deleted_at')
            // ->where('lines.id', $commercial['line'])
            ->where($table, $object->id)
            ->where('commercial_requests.request_type_id', $type)
            ->whereBetween('commercial_requests.created_at', [$from, $to]);
        $commercials = match ($commercial['approval']) {
            1 => $commercials->whereNull('plan_visit_details.approval'),
            2 => $commercials->where('plan_visit_details.approval', 1),
            3 => $commercials->where('plan_visit_details.approval', 0),
            4 => $commercials->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null)),
            5 => $commercials->where('approval_flow_users.user_id', $authUser->id),
            6 => $commercials,
        };
        $commercials = match ($commercial['payment']) {
            1 => $commercials->where('paid_requests.type', 'No'),
            2 => $commercials->where('paid_requests.type', 'Yes'),
            3 => $commercials->where('paid_requests.type', 'Partial'),
            4 => $commercials
        };
        $commercials = match ($commercial['archived']) {
            'No' => $commercials->where('commercial_requests.archived', 0),
            'Yes' => $commercials->where('commercial_requests.archived', 1),
            'Total' => $commercials,
        };
        if ($commercial['paymentFromDate'] && $commercial['paymentToDate']) {
            $commercials = $commercials->whereBetween('paid_requests.date', [$commercial['paymentFromDate'], $commercial['paymentToDate']]);
        }
        if (!empty($commercial['lines'])) {
            $commercials = $commercials->whereIntegerInRaw('commercial_lines.line_id', $commercial['lines']);
        }
        if (!empty($commercial['products'])) {
            $commercials = $commercials->whereIntegerInRaw('commercial_products.product_id', $commercial['products']);
        }

        $commercials = $commercials->groupBy("id", "paid_requests.id")->get();

        return $commercials;
    }
    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $commercial = $request->commercialFilter;
        $from = Carbon::parse($commercial['fromDate'])->startOfDay();
        $to = Carbon::parse($commercial['toDate'])->endOfDay();
        $year = Carbon::parse($from)->format('Y');
        $lines = Line::when(!empty($commercial['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $commercial['lines']))->get();
        $types = RequestType::select('request_types.id', 'request_types.name')
            ->when(!empty($commercial['types']), fn($q) => $q->whereIn("request_types.id", $commercial['types']))->get();
        $fields = collect(['line', 'division', 'employee', 'emp_code']);
        $clickable_fields = collect([]);
        foreach ($types as $type) {
            $fields = $fields->push($type->name . ' ' . '(number)');
            $fields = $fields->push($type->name . ' ' . '(amount)');
            $clickable_fields = $clickable_fields->push($type->name . ' ' . '(number)');
            $clickable_fields = $clickable_fields->push($type->name . ' ' . '(amount)');
        }
        $fields = $fields->merge(['total_number', 'total_amount']);

        $filtered = new Collection([]);
        $data = new Collection([]);
        foreach ($lines as $line) {
            if ($commercial['filter'] == 1) {
                $divisions = $line->divisions()->where("deleted_at", null)
                    ->when(!empty($commercial['divisions']), fn($q) => $q->whereIn("line_divisions.id", $commercial['divisions']))->get();
                $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $commercial));
            }
            if ($commercial['filter'] == 2) {
                $users = $line->users()->wherePivot("deleted_at", null)
                    ->when(!empty($commercial['users']), fn($q) => $q->whereIn("line_users.user_id", $commercial['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $commercial));
            }
        }
        $filtered->each(function ($object) use ($lines, $commercial, $data, $from, $to, $types,$authUser) {
            $data = $data->push($this->statistics($lines->pluck('id'), $object, $types, $commercial, $from, $to,$authUser));
        });
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'dates' => $dates,
            'clickable_fields' => $clickable_fields
        ]);
    }
    private function statistics($lineIds, $object, $types, $commercial, $from, $to,$authUser)
    {
        $lines = $commercial['filter'] == 1 ?  $object->line : $object->lines($from, $to)->whereIntegerInRaw('line_users.line_id', $lineIds)->get();
        $userLinesIds = $lines->pluck('id');
        $userData = $commercial['filter'] == 1
            ? $object->users()?->whereIntegerInRaw('line_users_divisions.line_id', $lineIds)
            : $object;
        $totalNumbers = 0;
        $totalAmounts = 0;
        $data = collect([
            'id' => $object->id,
            'line' => $commercial['filter'] == 1 ? $object?->line?->name : $lines->pluck('name')->implode(','),
            'division' => $commercial['filter'] == 1 ? $object?->name : $object->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $userLinesIds)->pluck('name')->implode(','),
            'employee' => $commercial['filter'] == 1 ? $userData->pluck('fullname')->implode(',') : $object?->fullname,
            'emp_code' => $commercial['filter'] == 1 ? $userData->pluck('emp_code')->implode(',') : $object?->emp_code,
            'color' => $commercial['filter'] == 1 ? $object?->DivisionType->color : $object->divisions($from, $to)?->first()?->DivisionType->color,
        ]);
        $types->each(function ($type) use (&$data, $commercial, $object, &$totalAmounts, &$totalNumbers, $from, $to,$authUser) {
            $commercials = $this->getCommercials($commercial, $object, $commercial['filter'] == 1 ? 'line_divisions.id' : 'users.id', $from, $to, $type->id,$authUser);
            $countType = $commercials->count();
            $sumAmount = $commercials->sum('total');
            $totalNumbers += $countType;
            $totalAmounts += $sumAmount;
            $data->put($type->name . ' ' . '(number)', $countType);
            $data->put($type->name . ' ' . '(amount)', round($sumAmount, 2));
        });
        $data->put('total_number', $totalNumbers);
        $data->put('total_amount', round($totalAmounts, 2));
        return $data;
    }
    public function showData(Request $request)
    {
        $commercial = $request->listFilter;
        $column = $request->column;
        $from = Carbon::parse($commercial['fromDate'])->startOfDay();
        $to = Carbon::parse($commercial['toDate'])->endOfDay();

        $types = RequestType::select('request_types.id', 'request_types.name')
            ->when(!empty($commercial['types']), fn($q) => $q->whereIn("request_types.id", $commercial['types']))->get();
        $fieldCalls = $types->map(function ($type) {
            return [
                'type_id' => $type->id,
                'number' => $type->name . ' ' . '(number)',
                'amount' => $type->name . ' ' . '(amount)',
            ];
        });
        $object = '';
        $commercials = collect([]);
        if ($request->div != null) {
            $object = LineDivision::find($request->div);
        } else {
            $object = User::find($request->user);
        }

        $data = null;
        $authUser = Auth::user();

        foreach ($fieldCalls as $field) {
            $commercials = $this->getCommercials($commercial, $object, $commercial['filter'] == 1 ? 'line_divisions.id' : 'users.id', $from, $to, $field['type_id'],$authUser);
            if ($field['number'] == $column) {
                $data = $commercials->values()->map(function ($commercial) {
                    return [
                        'id' => $commercial->id,
                        'line' => $commercial->line,
                        'div' => $commercial->division ?? '',
                        'emp' => $commercial->employee,
                        'type' => $commercial->type,
                        'from' => $commercial->from,
                        'to' => $commercial->to,
                        'product' => $commercial->product ?? '',
                        'ratio' => $commercial->ratio ?? '',
                        'pharmacy' => $commercial->rharmacy ?? '',
                        'description' => $commercial->description ?? '',
                        'status' => $this->getStatus($commercial->id),
                        'feedback' => $this->getFeedbacks($commercial->id),

                    ];
                });
                return $this->respond($data);
            }
            if ($field['amount'] == $column) {
                $data = $commercials->values()->map(function ($commercial) {
                    return [
                        'id' => $commercial->id,
                        'line' => $commercial->line,
                        'div' => $commercial->division ?? '',
                        'emp' => $commercial->employee,
                        'type' => $commercial->type,
                        'from' => $commercial->from,
                        'to' => $commercial->to,
                        'product' => $commercial->product ?? '',
                        'ratio' => $commercial->ratio ?? '',
                        'pharmacy' => $commercial->rharmacy ?? '',
                        'description' => $commercial->description ?? '',
                        'total' => $commercial->total,
                        'status' => $this->getStatus($commercial->id),
                        'feedback' => $this->getFeedbacks($commercial->id),
                    ];
                });
                return $this->respond($data);
            }
        }
    }
    private function getStatus($id)
    {
        $commercial = CommercialRequest::find($id);
        if ($commercial->details?->approval === null) return null;
        if ($commercial->details?->approval === 1) return 1;
        if ($commercial->details?->approval === 0) return 0;
    }
    private function getFeedbacks($id)
    {
        $commercial = CommercialRequest::find($id);
        if (count($commercial->feedbacks) > 0)  return $commercial->feedbacks->pluck('feedback');
        else return '';
    }
}
